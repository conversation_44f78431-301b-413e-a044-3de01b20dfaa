#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Tam entegre edilmiş program:
- Video indirme, yükleme, konfigürasyon işlemleri
- PyQt5 tabanlı modern arayüz (3 deck UI)
- Profil seçimi ve düzenlen<PERSON>i (pop-up pencere)
- "Saat Ayarları" (gün bazlı)
- "Link Ekle" (gün bazsız, tek listede link tutma)
- Link ekleme penceresi tekrar açıldığında, henüz indirilmeyen linkler JSON'dan okunup görüntülenir.
- Artık instagram_links.txt / youtube_links.txt / twitter_links.txt DOSYALARI KULLANILMAZ,
  indirme kaynağı olarak ilgili profilin .json içindeki "links" listesi kullanılır.
"""

import sys
import os
import types
import cv2  # OpenCV, video süresi, boyut ve kare yakalama işlemleri için kullanılacak
import instaloader
import time
import logging
import shutil
import re
import yt_dlp
import zipfile
import requests
import urllib.request
import platform
import random
import json
import threading
import queue
import base64
import zipfile, urllib.request
from collections import deque
from urllib.parse import urlparse
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from pathlib import Path
import psutil

# Selenium ve undetected_chromedriver importları (Twitter upload için)
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import undetected_chromedriver as uc

# Instagrapi (Instagram API için)
from instagrapi import Client
from instagrapi.exceptions import LoginRequired

from PyQt5.QtCore import (
    QCoreApplication, Qt, QObject, pyqtSlot, QTime, QPoint, QEvent, pyqtSignal, QTimer
)
from PyQt5.QtGui import QCursor, QColor, QFont
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QDesktopWidget, QDialog,
    QVBoxLayout, QHBoxLayout, QLabel, QWidget, QPushButton,
    QFormLayout, QLineEdit, QTimeEdit, QGraphicsDropShadowEffect,
    QFrame, QMenu, QPlainTextEdit
)
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel

chromium_links = {
    "win64": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win_x64%2F1110125%2Fchrome-win.zip?generation=1677456389285817&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win_x64%2F1110125%2Fchromedriver_win32.zip?generation=1677456585621401&alt=media"
    },
    "win32": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win%2F1110036%2Fchrome-win.zip?generation=1677368775095363&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win%2F1110036%2Fchromedriver_win32.zip?generation=1677368988947504&alt=media"
    },
    "mac": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac%2F1110036%2Fchrome-mac.zip?generation=1677366724785907&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac%2F1110036%2Fchromedriver_mac64.zip?generation=1677366732746411&alt=media"
    },
    "mac_arm": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac_Arm%2F1110125%2Fchrome-mac.zip?generation=1677454456625954&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac_Arm%2F1110125%2Fchromedriver_mac64.zip?generation=1677454462117324&alt=media"
    },
    "linux_x64": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Linux_x64%2F1109848%2Fchrome-linux.zip?generation=1677278713298627&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Linux_x64%2F1109848%2Fchromedriver_linux64.zip?generation=1677278719174033&alt=media"
    }
}


# 2. Platform Tespiti İçin Fonksiyon Ekleyin
def detect_platform_key():
    system = platform.system()
    arch = platform.architecture()[0]

    if system == "Windows":
        return "win64" if "64" in arch else "win32"
    elif system == "Darwin":
        machine = platform.machine().lower()
        return "mac_arm" if "arm" in machine else "mac"
    elif system == "Linux":
        return "linux_x64"
    else:
        raise Exception("Desteklenmeyen işletim sistemi")


# 3. İndirme ve Çıkarma Fonksiyonunu Ekleyin

def download_and_extract(url, extract_to):
    os.makedirs(extract_to, exist_ok=True)
    zip_path = os.path.join(extract_to, "temp.zip")
    urllib.request.urlretrieve(url, zip_path)
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_to)
    os.remove(zip_path)


def setup_portable_chromium():
    platform_key = detect_platform_key()
    links = chromium_links[platform_key]

    base_dir = Path(__file__).resolve().parent / "portable_chromium"
    browser_dir = base_dir / "browser"
    driver_dir = base_dir / "driver"

    # Browser için: ZIP açıldığında oluşan dizin ismine dikkat edin.
    if platform_key.startswith("win"):
        expected_browser_folder = "chrome-win"
    elif platform_key in ["mac", "mac_arm"]:
        expected_browser_folder = "chrome-mac"
    elif platform_key == "linux_x64":
        expected_browser_folder = "chrome-linux"
    else:
        raise Exception("Bilinmeyen platform")

    if not (browser_dir / expected_browser_folder).exists():
        download_and_extract(links["browser_zip"], str(browser_dir))

    # Chromedriver için: Eğer klasörde chromedriver yoksa indir.
    if not os.path.exists(str(driver_dir)):
        os.makedirs(str(driver_dir), exist_ok=True)
    if not any("chromedriver" in f for f in os.listdir(str(driver_dir)) if
               os.path.isfile(os.path.join(str(driver_dir), f))):
        download_and_extract(links["driver_zip"], str(driver_dir))

    # Binary yolu belirleme:
    if platform_key.startswith("win"):
        binary_path = str(browser_dir / expected_browser_folder / "chrome.exe")
    elif platform_key in ["mac", "mac_arm"]:
        binary_path = str(browser_dir / expected_browser_folder / "Chromium.app" / "Contents" / "MacOS" / "Chromium")
    elif platform_key == "linux_x64":
        binary_path = str(browser_dir / expected_browser_folder / "chrome")
    else:
        raise Exception("Bilinmeyen platform için binary yolu ayarlanamadı.")

    # Chromedriver yolunu bulma:
    driver_path = None
    for root, dirs, files in os.walk(str(driver_dir)):
        for file in files:
            if "chromedriver" in file:
                driver_path = os.path.join(root, file)
                break
        if driver_path:
            break
    if not driver_path:
        raise Exception("Chromedriver bulunamadı!")

    return binary_path, driver_path


def get_portable_chromium_options():
    binary_path, driver_path = setup_portable_chromium()
    options = uc.ChromeOptions()
    options.binary_location = binary_path
    options.add_argument("--headless")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--disable-infobars")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-gpu")
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-popup-blocking")
    options.add_argument("--disable-notifications")
    options.add_argument("--lang=en-US")
    options.add_argument("--window-size=1920,1080")
    return options, driver_path


# GPU'suz sistemlerde yazılım OpenGL render kullanımı için gerekli ayarlar:
os.environ["QTWEBENGINE_CHROMIUM_FLAGS"] = "--disable-gpu --disable-software-rasterizer"
QCoreApplication.setAttribute(Qt.AA_UseSoftwareOpenGL)

# ----------------------------------------------------------------------------------
# TELEGRAM BİLGİLERİNİ BURAYA GİRİN (TOKEN VE CHAT_ID):
# ----------------------------------------------------------------------------------
TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_CHAT_ID = "615971252"


# ----------------------------------------------------------------------------------

# ----------------------------------------------------------------------------------
# İSTATİSTİK SİSTEMİ DEĞİŞKENLERİ
# ----------------------------------------------------------------------------------
class StatisticsSystem:
    def __init__(self):
        self.stats_queue = queue.Queue()  # İstatistik çekme işleri için kuyruk
        self.profiles_to_process = set()  # İşlenecek profillerin kümesi
        self.stats_data = {}  # Her profilin istatistik verilerini saklayan sözlük
        self.stats_display_queue = deque()  # Ekranda gösterilecek istatistiklerin sırası
        self.last_profile_check = datetime.now()  # Son profil kontrol zamanı
        self.last_stats_update = {}  # Her profilin son istatistik güncelleme zamanı
        self.current_displayed_profile = None  # Şu an gösterilen profil
        self.last_display_change = datetime.now()  # Son gösterim değişikliği zamanı
        self.is_processing = False  # İstatistik çekme işlemi yapılıyor mu?
        self.worker_thread = None  # İstatistik çekme thread'i
        self.initialized = False  # Sistem başlatıldı mı?

    def istatistikleri_kaydet(self):
        """
        Tüm istatistik verilerini ve güncelleme zamanlarını dosyaya kaydeder.
        """
        try:
            base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio\\configuration"
            stats_file = os.path.join(base_dir, "istatistikler.json")
            with open(stats_file, "w", encoding="utf-8") as f:
                json.dump({
                    "stats_data": self.stats_data,
                    "last_stats_update": {k: v.strftime("%Y-%m-%d %H:%M:%S") for k, v in self.last_stats_update.items()}
                }, f, indent=4, ensure_ascii=False)
        except Exception as e:
            logging.error(f"İstatistikler kaydedilemedi: {str(e)}")

    def istatistikleri_yukle(self):
        """
        Daha önce kaydedilmiş istatistik verilerini ve güncelleme zamanlarını dosyadan yükler.
        """
        try:
            base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio\\configuration"
            stats_file = os.path.join(base_dir, "istatistikler.json")
            if not os.path.exists(stats_file):
                return
            with open(stats_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                self.stats_data = data.get("stats_data", {})
                # last_stats_update string olarak kaydedildiği için tekrar datetime'a çeviriyoruz
                self.last_stats_update = {k: datetime.strptime(v, "%Y-%m-%d %H:%M:%S") for k, v in
                                          data.get("last_stats_update", {}).items()}
        except Exception as e:
            logging.error(f"İstatistikler yüklenemedi: {str(e)}")

    def profil_istatistiklerini_sil(self, profile_key):
        """
        Verilen profile_key'e ait tüm istatistik verilerini ve güncelleme zamanını sistemden ve dosyadan siler.
        Ayrıca istatistik deckindeki gösterimini anında sonlandırır.
        """
        try:
            if profile_key in self.stats_data:
                del self.stats_data[profile_key]
            if profile_key in self.last_stats_update:
                del self.last_stats_update[profile_key]
            if profile_key in self.stats_display_queue:
                self.stats_display_queue.remove(profile_key)

            # Eğer silinen profil şu an gösterilen profilse, başka bir profile geç
            if self.current_displayed_profile == profile_key:
                self.current_displayed_profile = next(iter(self.stats_display_queue), None)

            # İstatistikleri kaydet
            self.istatistikleri_kaydet()

            # Arayüzü hemen güncelle (her zaman UI thread'inde ve asenkron)
            try:
                from PyQt5.QtCore import QTimer
                from PyQt5.QtWidgets import QApplication
                for widget in QApplication.topLevelWidgets():
                    if hasattr(widget, "update_stats"):
                        QTimer.singleShot(0, widget.update_stats)
                        break
            except Exception as e:
                logging.error(f"Profil silindikten sonra UI güncellenirken hata: {str(e)}")

            logging.info(f"{profile_key} için istatistik verileri tamamen silindi.")
        except Exception as e:
            logging.error(f"{profile_key} için istatistik silinirken hata: {str(e)}")


# Global istatistik sistemi nesnesi
stats_system = StatisticsSystem()


def send_telegram_message(bot_token, chat_id, message_text):
    """
    Girilen mesajı Telegram üzerinden belirtilen chat_id'ye gönderir.
    """
    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    payload = {
        "chat_id": chat_id,
        "text": message_text
    }
    try:
        requests.post(url, data=payload)
        logging.info("DESTEK mesajı Telegram'a gönderildi.")
    except Exception as e:
        logging.error(f"Telegram mesajı gönderilemedi: {str(e)}")


# ---------- Dummy moviepy.editor modülü oluşturma ----------
def dummy_duration(path):
    try:
        cap = cv2.VideoCapture(path)
        if not cap.isOpened():
            return 30  # Açılamayan video için varsayılan süre
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps == 0:
            return 30
        frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
        duration = frame_count / fps
        cap.release()
        return duration
    except Exception:
        return 30


# Eğer "moviepy.editor" yüklü değilse dummy modülü ekleyelim
if "moviepy.editor" not in sys.modules:
    dummy_module = types.ModuleType("moviepy.editor")


    class DummyVideoFileClip:
        def __init__(self, path):
            self.path = path
            self.duration = dummy_duration(path)
            try:
                cap = cv2.VideoCapture(self.path)
                if cap.isOpened():
                    width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                    height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                    self.size = (int(width), int(height))
                else:
                    self.size = (640, 480)
                cap.release()
            except Exception:
                self.size = (640, 480)

        def save_frame(self, filename, t=0, withmask=True, **kwargs):
            cap = cv2.VideoCapture(self.path)
            if not cap.isOpened():
                cap.release()
                raise Exception("Video açılamadı.")
            fps = cap.get(cv2.CAP_PROP_FPS)
            if fps == 0:
                cap.release()
                raise Exception("FPS değeri 0.")
            frame_index = int(t * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
            ret, frame = cap.read()
            if ret:
                cv2.imwrite(filename, frame)
            else:
                raise Exception("Belirtilen zamanda kare yakalanamadı.")
            cap.release()

        def close(self):
            pass


    dummy_module.VideoFileClip = DummyVideoFileClip
    sys.modules["moviepy.editor"] = dummy_module


def format_stats_html(stats_data):
    """
    Verilen stats_data sözlüğüne göre, grid halinde 3 sütunlu HTML kutucukları üretir.
    """
    platform = stats_data.get("platform", "platform")
    username = stats_data.get("username", "kullanıcı")
    timestamp = stats_data.get("timestamp", "")
    metrics = stats_data.get("metrics", {})

    html = f"""
    <div style="padding:8px; background-color:#1E1E1E; font-family: 'Segoe UI', sans-serif;">
        <div style="font-size:13px; font-weight:bold; color:#FFFFFF;">{platform.capitalize()} İstatistikleri</div>
        <div style="font-size:11px; color:#FFFFFF;">@{username}</div>
        <div style="font-size:11px; color:#AAAAAA;">Güncelleme: {timestamp}</div>
        <div style="margin-top:8px; font-size:12px; font-weight:bold; color:#FFFFFF;">Son 7 Günün İstatistiği</div>
        <div style="display:grid; grid-template-columns: repeat(3, 1fr); gap:6px; margin-top:8px;">
    """

    for metric_name, metric_data in metrics.items():
        if isinstance(metric_data, dict):
            val = metric_data.get("value", "N/A")
            trend = metric_data.get("trend", "")
        else:
            val = metric_data
            trend = ""

        if "^" in trend:
            trend_color = "#4CAF50"
        elif "v" in trend:
            trend_color = "#F44336"
        else:
            trend_color = "#AAAAAA"

        html += f"""
            <div style="background-color:#2A2A2A; border:1px solid #373737; border-radius:6px;
                        min-height:45px; display:flex; align-items:center; justify-content:center;
                        font-size:11px; font-weight:bold; color:#FFFFFF;">
                {metric_name}
            </div>
            <div style="background-color:#2A2A2A; border:1px solid #373737; border-radius:6px;
                        min-height:45px; display:flex; align-items:center; justify-content:center;
                        font-size:12px; font-weight:bold; color:#FFFFFF;">
                {val}
            </div>
            <div style="background-color:#2A2A2A; border:1px solid #373737; border-radius:6px;
                        min-height:45px; display:flex; align-items:center; justify-content:center;
                        font-size:12px; font-weight:bold; color:{trend_color};">
                {trend}
            </div>
        """
    html += "</div></div>"
    return html


def format_instagram_stats_as_two_columns(stats_data):
    """
    Instagram istatistiklerini alt alta (tek sütun) kutucuklar halinde düzenler.
    Profil adının yanına Instagram logosu eklenir, diğer yapılar değişmeden korunur.
    """
    if not stats_data or "metrics" not in stats_data:
        return "<div style='color:#FFFFFF; font-family:Segoe UI;'>İstatistik verisi bulunamadı</div>"

    username = stats_data.get("username", "")
    metrics = stats_data.get("metrics", {})

    try:
        logo_path = os.path.join(os.path.dirname(__file__), "instagram.png")
        with open(logo_path, "rb") as img_f:
            b64_logo = base64.b64encode(img_f.read()).decode()
        logo_html = f'<img src="data:image/png;base64,{b64_logo}" style="height:36px; width:auto; vertical-align:middle; margin-right:6px;" />'
    except Exception:
        logo_html = '<div style="width:36px; height:36px; margin-right:6px;"></div>'

    base_box_style = (
        "background-color:#2A2A2A;"
        "border:1px solid #373737;"
        "border-radius:6px;"
        "padding:10px;"
        "font-size:12px;"
        "color:#FFFFFF;"
        "margin-bottom:8px;"
        "display:flex;"
        "flex-direction:column;"
        "align-items:center;"
        "text-align:center;"
    )

    html = f'''
    <div style="font-family:Segoe UI; background-color:#1E1E1E; padding:10px;">
      <div style="display:flex; align-items:center; gap:6px; font-size:16px; font-weight:bold; color:#FFFFFF; margin-bottom:15px; letter-spacing:0.5px;">
        {logo_html}
        <div style="font-size:16px; font-weight:bold; color:#FFFFFF;">{username}</div>
      </div>
      <div style="display:flex; flex-direction:column;">
    '''

    followers = metrics.get("Takipçi Sayısı", {})
    html += f'''
      <div style="{base_box_style}">
        <div style="font-size:15px; font-weight:600; color:#DDDDDD; margin-bottom:6px;">Takipçi Sayısı</div>
        <div style="font-size:22px; font-weight:700;">{followers.get("value", "N/A")}</div>
      </div>
    '''

    for metric_name in ["Gösterimler", "Erişim", "Profil Ziyaretleri"]:
        metric_data = metrics.get(metric_name, {})
        value = metric_data.get("value", "N/A")
        trend = metric_data.get("trend", "")
        delta = metric_data.get("delta", 0)

        if delta > 0:
            trend_color = "#4CAF50"
        elif delta < 0:
            trend_color = "#F44336"
        else:
            trend_color = "#FFFFFF"
            trend = "0"

        html += f'''
      <div style="{base_box_style}">
        <div style="font-size:15px; font-weight:600; color:#DDDDDD; margin-bottom:6px;">{metric_name}</div>
        <div style="font-size:22px; font-weight:700;">{value}</div>
        <div style="font-size:14px; font-weight:bold; color:{trend_color}; margin-top:4px;">{trend}</div>
      </div>
    '''

    metric_data = metrics.get("Takipçi Değişimi", {})
    delta = metric_data.get("delta", 0)
    trend = metric_data.get("trend", "")

    if delta > 0:
        trend_color = "#4CAF50"
    elif delta < 0:
        trend_color = "#F44336"
    else:
        trend_color = "#FFFFFF"
        trend = "0"

    html += f'''
      <div style="{base_box_style}">
        <div style="font-size:15px; font-weight:600; color:#DDDDDD; margin-bottom:6px;">Takipçi Değişimi</div>
        <div style="font-size:22px; font-weight:700; color:{trend_color};">{trend}</div>
      </div>
    '''

    html += f'''
      </div>
      <div style="font-size:11px; color:#AAAAAA; text-align:right; margin-top:10px;">
        Son güncelleme: {stats_data.get("timestamp", "")}
      </div>
    </div>
    '''

    return html


# ---------- ffmpeg İndirme Fonksiyonu ----------
def download_ffmpeg():
    """ffmpeg'i indirir ve kurar"""
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    videos_dir = os.path.join(base_dir, "videos")
    os.makedirs(videos_dir, exist_ok=True)
    ffmpeg_dir = os.path.join(videos_dir, "ffmpeg")
    ffmpeg_exe = os.path.join(ffmpeg_dir, "ffmpeg.exe")

    if os.path.exists(ffmpeg_exe):
        # PATH'e ekleyelim ki yt-dlp/instaloader bulsun
        os.environ["PATH"] = ffmpeg_dir + os.pathsep + os.environ.get("PATH", "")
        return ffmpeg_dir

    try:
        os.makedirs(ffmpeg_dir, exist_ok=True)
        ffmpeg_url = (
            "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/"
            "ffmpeg-master-latest-win64-gpl.zip"
        )
        logging.info("ffmpeg indiriliyor.")
        response = requests.get(ffmpeg_url, stream=True)
        zip_path = os.path.join(videos_dir, "ffmpeg.zip")
        with open(zip_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        logging.info("ffmpeg paketi açılıyor.")
        temp_extract = os.path.join(videos_dir, "ffmpeg_temp")
        with zipfile.ZipFile(zip_path, "r") as zip_ref:
            zip_ref.extractall(temp_extract)
            ffmpeg_bin = None
            for root, dirs, files in os.walk(temp_extract):
                if "ffmpeg.exe" in files:
                    ffmpeg_bin = os.path.join(root, "ffmpeg.exe")
                    break
            if ffmpeg_bin:
                shutil.copy2(ffmpeg_bin, ffmpeg_exe)
        shutil.rmtree(temp_extract)
        os.remove(zip_path)
        logging.info("ffmpeg kurulumu tamamlandı")
        # PATH'e ekleyelim ki ffmpeg her yerden çağrılabilsin
        os.environ["PATH"] = ffmpeg_dir + os.pathsep + os.environ.get("PATH", "")
        return ffmpeg_dir

    except Exception as e:
        logging.error(f"ffmpeg kurulumu sırasında hata oluştu: {str(e)}")
        return None


# ---------- Loglama ve Klasör İşlemleri ----------
def setup_logging():
    """Loglama sistemini ayarlar"""
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    videos_dir = os.path.join(base_dir, "videos")
    os.makedirs(videos_dir, exist_ok=True)
    log_file = os.path.join(videos_dir, "social_downloader.log")
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(log_file, encoding="utf-8"),
            logging.StreamHandler(),
        ],
    )
    return logging.getLogger(__name__)


# ----------------------------------------------------------------------------------
# İSTATİSTİK ÇEKME FONKSİYONLARI
# ----------------------------------------------------------------------------------
def extract_instagram_metrics(username, password):
    """
    Instagram API ile istatistikleri çeker ve session yönetimini gelişmiş şekilde yapar.
    Session dosyası ile birlikte bir meta dosyası (<username>_session.meta.json) tutulur.
    - created: oluşturulma zamanı (timestamp)
    - last_used: son kullanım zamanı (timestamp)
    - next_refresh: bir sonraki zorunlu yenileme zamanı (timestamp)
    Session 48 saatten eskiyse, 48-72 veya 72-96 saat aralığında random bir yenileme zamanı atanır.
    Her kullanımda last_used güncellenir.
    """
    import json
    from datetime import datetime, timedelta
    import random
    import os
    import logging

    base_config_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio\\configuration\\instagram"
    session_dir = os.path.join(base_config_dir, "sessions")
    os.makedirs(session_dir, exist_ok=True)
    session_file = os.path.join(session_dir, f"{username}_session.json")
    meta_file = os.path.join(session_dir, f"{username}_session.meta.json")
    cl = Client()

    now = datetime.now()
    meta = {
        "created": None,
        "last_used": None,
        "next_refresh": None
    }
    # Meta dosyasını oku veya oluştur
    if os.path.exists(meta_file):
        try:
            with open(meta_file, "r", encoding="utf-8") as mf:
                meta = json.load(mf)
        except Exception:
            pass

    # Zamanları datetime objesine çevir
    def parse_time(val):
        if not val:
            return None
        try:
            return datetime.strptime(val, "%Y-%m-%d %H:%M:%S")
        except Exception:
            return None

    created = parse_time(meta.get("created"))
    last_used = parse_time(meta.get("last_used"))
    next_refresh = parse_time(meta.get("next_refresh"))

    session_valid = False
    session_needs_refresh = False
    session_exists = os.path.exists(session_file)

    if session_exists and created:
        age = (now - created).total_seconds() / 3600  # saat cinsinden
        # 48 saatten eskiyse, next_refresh atanmış mı bak
        if age >= 6:
            if not next_refresh or next_refresh < now:
                # 6-15 saat aralığında random bir zaman ata
                min_dt = created + timedelta(hours=6)
                max_dt = created + timedelta(hours=15)
                random_seconds = random.randint(0, int((max_dt - min_dt).total_seconds()))
                next_refresh = min_dt + timedelta(seconds=random_seconds)
                meta["next_refresh"] = next_refresh.strftime("%Y-%m-%d %H:%M:%S")
                # Meta dosyasını hemen güncelle
                with open(meta_file, "w", encoding="utf-8") as mf:
                    json.dump(meta, mf, indent=4, ensure_ascii=False)
            # Yenileme zamanı gelmiş mi?
            if next_refresh and now >= next_refresh:
                session_needs_refresh = True
            else:
                session_valid = True
        else:
            session_valid = True
    elif session_exists:
        # Meta yoksa veya bozuksa, session dosyasının dosya sisteminden oluşturulma zamanını kullan
        try:
            ctime = datetime.fromtimestamp(os.path.getctime(session_file))
            created = ctime
            meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
            meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
            meta["next_refresh"] = ""
            with open(meta_file, "w", encoding="utf-8") as mf:
                json.dump(meta, mf, indent=4, ensure_ascii=False)
            session_valid = True
        except Exception:
            session_valid = False

    try:
        logging.info(f"Instagram istatistikleri çekiliyor: {username}")
        if session_exists and session_valid and not session_needs_refresh:
            try:
                cl.load_settings(session_file)
                # Oturum test
                try:
                    logging.info(f"Oturum test ediliyor (metrics - {username}) [get_timeline_feed]...")
                    cl.get_timeline_feed()
                    logging.info(f"Oturum testi başarılı (metrics - {username}). Session kullanılacak.")
                except Exception as e_test:
                    logging.warning(
                        f"Oturum testi başarısız (metrics - {username}): {e_test}. Yeniden giriş yapılacak ve oturum güncellenecek.")
                    cl.login(username, password)
                    cl.dump_settings(session_file)
                    # Meta güncelle
                    created = now
                    meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                    meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                    meta["next_refresh"] = ""
                    with open(meta_file, "w", encoding="utf-8") as mf:
                        json.dump(meta, mf, indent=4, ensure_ascii=False)
                # Başarılıysa last_used güncelle
                meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                with open(meta_file, "w", encoding="utf-8") as mf:
                    json.dump(meta, mf, indent=4, ensure_ascii=False)
            except Exception as e:
                logging.warning(f"Oturum yükleme veya kullanma hatası ({e}), normal giriş deneniyor.")
                cl.login(username, password)
                cl.dump_settings(session_file)
                # Meta güncelle
                created = now
                meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                meta["next_refresh"] = ""
                with open(meta_file, "w", encoding="utf-8") as mf:
                    json.dump(meta, mf, indent=4, ensure_ascii=False)
        else:
            # Session yok veya yenileme zamanı gelmiş
            cl.login(username, password)
            cl.dump_settings(session_file)
            created = now
            meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
            meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
            meta["next_refresh"] = ""
            with open(meta_file, "w", encoding="utf-8") as mf:
                json.dump(meta, mf, indent=4, ensure_ascii=False)
        # API'dan veri çek
        try:
            insights = cl.insights_account()
            profile_info = cl.user_info_by_username(username)
            logging.info(f"API'dan data çekildi: {username}")
        except LoginRequired as lr_exc:
            logging.warning(
                f"Instagram API çağrısı LoginRequired hatası verdi ({lr_exc}). Session silinip yeniden giriş denenecek: {username}")
            if os.path.exists(session_file):
                try:
                    os.remove(session_file)
                    logging.info(f"Eski/sorunlu session dosyası silindi: {session_file}")
                except Exception as rm_err:
                    logging.error(f"Session dosyası ({session_file}) silinirken hata: {rm_err}")
            if os.path.exists(meta_file):
                try:
                    os.remove(meta_file)
                except Exception:
                    pass
            logging.info(f"Zorunlu yeniden giriş yapılıyor: {username}")
            cl.login(username, password)
            logging.info(f"Zorunlu yeniden giriş başarılı: {username}")
            cl.dump_settings(session_file)
            created = now
            meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
            meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
            meta["next_refresh"] = ""
            with open(meta_file, "w", encoding="utf-8") as mf:
                json.dump(meta, mf, indent=4, ensure_ascii=False)
            # Retry fetching data
            insights = cl.insights_account()
            profile_info = cl.user_info_by_username(username)
            logging.info(f"Zorunlu yeniden giriş sonrası data başarıyla çekildi: {username}")

        # None değerleri 0 ile değiştir
        def handle_none(value):
            return 0 if value is None else value

        followers_count = handle_none(profile_info.follower_count)
        impressions_count = handle_none(insights.get("account_insights_unit", {}).get("impressions_metric_count", 0))
        impressions_delta = handle_none(insights.get("account_insights_unit", {}).get("impressions_metric_delta", 0))
        reach_count = handle_none(insights.get("account_insights_unit", {}).get("reach_metric_count", 0))
        reach_delta = handle_none(insights.get("account_insights_unit", {}).get("reach_metric_delta", 0))
        profile_visits_count = handle_none(
            insights.get("account_insights_unit", {}).get("profile_visits_metric_count", 0))
        profile_visits_delta = handle_none(
            insights.get("account_insights_unit", {}).get("profile_visits_metric_delta", 0))
        followers_delta = handle_none(insights.get("followers_unit", {}).get("followers_delta_from_last_week", 0))
        metrics = {
            "Takipçi Sayısı": {
                "value": followers_count,
                "trend": ""
            },
            "Gösterimler": {
                "value": impressions_count,
                "trend": f"{impressions_delta:+d}",
                "delta": impressions_delta
            },
            "Erişim": {
                "value": reach_count,
                "trend": f"{reach_delta:+d}",
                "delta": reach_delta
            },
            "Profil Ziyaretleri": {
                "value": profile_visits_count,
                "trend": f"{profile_visits_delta:+d}",
                "delta": profile_visits_delta
            },
            "Takipçi Değişimi": {
                "value": "",
                "trend": f"{followers_delta:+d}",
                "delta": followers_delta
            }
        }
        return {
            "platform": "instagram",
            "username": username,
            "timestamp": now.strftime("%Y-%m-%d %H:%M:%S"),
            "metrics": metrics
        }
    except LoginRequired as final_lr_exc:
        logging.error(
            f"Instagram istatistik çekme hatası: LoginRequired (yeniden deneme sonrası da devam ediyor) ({username}): {final_lr_exc}")
        if os.path.exists(session_file):
            try:
                os.remove(session_file)
                logging.info(
                    f"Başarısız (LoginRequired) yeniden deneme sonrası session dosyası silindi: {session_file}")
            except Exception as rm_err:
                logging.error(f"Session dosyası ({session_file}) silinirken (LoginRequired sonrası) hata: {rm_err}")
        if os.path.exists(meta_file):
            try:
                os.remove(meta_file)
            except Exception:
                pass
        return None
    except Exception as e:
        logging.error(f"Genel Instagram istatistik çekme hatası ({username}): {str(e)}")
        if "login" in str(e).lower() and os.path.exists(session_file):
            logging.warning(f"Genel hata 'login' içeriyor, session dosyası ({session_file}) siliniyor.")
            try:
                os.remove(session_file)
                logging.info(f"Genel 'login' hatası sonrası session dosyası silindi: {session_file}")
            except Exception as rm_s_err:
                logging.error(
                    f"Genel 'login' hatası sonrası session dosyası ({session_file}) silinirken hata: {rm_s_err}")
        if os.path.exists(meta_file):
            try:
                os.remove(meta_file)
            except Exception:
                pass
        return None


def extract_twitter_metrics(username, password):
    """
    Twitter istatistiklerini çeker ve metrikleri sözlük olarak döndürür.
    Twitter yazı ve ok işaretlerinden yüzde değişim değerlerini de ayıklar.
    Twitter oturum meta dosyası ile tazelik kontrolü eklenmiştir.
    """
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    import time
    from datetime import datetime, timedelta
    import re
    import os
    import json
    import random
    import logging

    # --- Twitter session meta yönetimi ---
    base_config_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio\\configuration\\twitter"
    session_dir = os.path.join(base_config_dir, "sessions")
    os.makedirs(session_dir, exist_ok=True)
    meta_file = os.path.join(session_dir, f"{username}_session.meta.json")

    now = datetime.now()
    meta = {
        "created": None,
        "last_used": None,
        "next_refresh": None
    }
    # Meta dosyasını oku veya oluştur
    if os.path.exists(meta_file):
        try:
            with open(meta_file, "r", encoding="utf-8") as mf:
                meta = json.load(mf)
        except Exception:
            pass

    def parse_time(val):
        if not val:
            return None
        try:
            return datetime.strptime(val, "%Y-%m-%d %H:%M:%S")
        except Exception:
            return None

    created = parse_time(meta.get("created"))
    last_used = parse_time(meta.get("last_used"))
    next_refresh = parse_time(meta.get("next_refresh"))

    session_valid = False
    session_needs_refresh = False
    session_exists = True  # Twitter'da session dosyası yok, sadece meta ile kontrol

    if created:
        age = (now - created).total_seconds() / 3600  # saat cinsinden
        # 8 saatten eskiyse, next_refresh atanmış mı bak
        if age >= 8:
            if not next_refresh or next_refresh < now:
                # 8-12 saat aralığında random bir zaman ata
                min_dt = created + timedelta(hours=8)
                max_dt = created + timedelta(hours=12)
                random_seconds = random.randint(0, int((max_dt - min_dt).total_seconds()))
                next_refresh = min_dt + timedelta(seconds=random_seconds)
                meta["next_refresh"] = next_refresh.strftime("%Y-%m-%d %H:%M:%S")
                # Meta dosyasını hemen güncelle
                with open(meta_file, "w", encoding="utf-8") as mf:
                    json.dump(meta, mf, indent=4, ensure_ascii=False)
            # Yenileme zamanı gelmiş mi?
            if next_refresh and now >= next_refresh:
                session_needs_refresh = True
            else:
                session_valid = True
        else:
            session_valid = True
    else:
        # Meta yoksa veya bozuksa, ilk girişte oluştur
        created = now
        meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
        meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
        meta["next_refresh"] = ""
        with open(meta_file, "w", encoding="utf-8") as mf:
            json.dump(meta, mf, indent=4, ensure_ascii=False)
        session_valid = True

    # --- Twitter oturumunu yenileme ---
    if session_needs_refresh:
        # Oturum klasörünü sil (chrome_profile_<username>)
        base_project_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
        user_profile_dir_path = os.path.join(base_project_dir, f"chrome_profile_{username}")
        if os.path.exists(user_profile_dir_path):
            import shutil
            try:
                shutil.rmtree(user_profile_dir_path)
                logging.info(f"Twitter oturum klasörü silindi (tazeleme): {user_profile_dir_path}")
            except Exception as e:
                logging.warning(f"Twitter oturum klasörü silinemedi (tazeleme): {user_profile_dir_path} - {e}")
        # Meta güncelle
        created = now
        meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
        meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
        meta["next_refresh"] = ""
        with open(meta_file, "w", encoding="utf-8") as mf:
            json.dump(meta, mf, indent=4, ensure_ascii=False)

    # --- Kalan orijinal kod ---
    def setup_driver():
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--lang=en-US')
        options.add_argument('--force-language=en')
        options.add_experimental_option('prefs', {
            'intl.accept_languages': 'en-US,en',
        })
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--window-size=1920,1080')
        driver = uc.Chrome(options=options, version_main=135)
        return driver
    def login_twitter(driver, username, password):
        driver.get("https://twitter.com/login?lang=en")
        time.sleep(3)
        try:
            accept_cookies = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, '//span[contains(text(), "Accept all cookies")]'))
            )
            accept_cookies.click()
            time.sleep(2)
        except:
            pass
        username_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[autocomplete="username"]'))
        )
        username_input.send_keys(username)
        username_input.send_keys(Keys.RETURN)
        time.sleep(2)
        password_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="password"]'))
        )
        password_input.send_keys(password)
        password_input.send_keys(Keys.RETURN)
        time.sleep(5)
        return True
    def extract_full_text(driver):
        driver.get("https://x.com/i/account_analytics")
        time.sleep(5)
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        full_text = driver.find_element(By.TAG_NAME, "body").text
        return full_text
    def parse_metrics(full_text, metric_labels):
        lines = full_text.splitlines()
        metrics = {}
        for label in metric_labels:
            indices = [i for i, line in enumerate(lines) if line.strip() == label]
            if indices:
                i = indices[-1]
                main_value = lines[i + 1] if i + 1 < len(lines) else "N/A"
                arrow = ""
                trend = ""
                for j in range(i + 2, len(lines)):
                    if lines[j] in ["↑", "↓", "^", "v"]:
                        arrow = lines[j]
                        trend = lines[j + 1] if j + 1 < len(lines) else ""
                        break
                percent_value = ""
                if trend:
                    percent_match = re.search(r'(-?\d+(\.\d+)?)%', trend)
                    if percent_match:
                        percent_value = percent_match.group(1) + "%"
                if arrow and trend:
                    metrics[label] = {
                        "value": main_value,
                        "trend": f"{arrow} {trend}",
                        "percent": percent_value,
                        "direction": "up" if arrow in ["↑", "^"] else "down"
                    }
                else:
                    metrics[label] = {
                        "value": main_value,
                        "trend": "",
                        "percent": "",
                        "direction": ""
                    }
        return metrics
    def click_4w_button(driver):
        try:
            button_4w = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//button[.//div[text()="4W"]]'))
            )
            button_4w.click()
            time.sleep(5)
            return True
        except Exception:
            return False
    driver = setup_driver()
    try:
        if not login_twitter(driver, username, password):
            return None
        full_text_7d = extract_full_text(driver)
        is_premium = full_text_7d and "Unlock Analytics with X Premium" not in full_text_7d
        if is_premium:
            metric_labels = [
                "Impressions", "Engagement rate", "Engagements", "Profile visits",
                "Replies", "Likes", "Reposts", "Bookmarks", "Shares"
            ]
        else:
            metric_labels = [
                "Impressions", "Likes", "Profile visits", "New follows", "Replies", "Reposts"
            ]
        metrics_7d = parse_metrics(full_text_7d, metric_labels)
        metrics = metrics_7d
        if is_premium and click_4w_button(driver):
            full_text_4w = extract_full_text(driver)
            metrics_4w = parse_metrics(full_text_4w, metric_labels)
            metrics["4W"] = metrics_4w
        # --- Twitter meta güncelle (her çekimde last_used güncellenir) ---
        meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
        with open(meta_file, "w", encoding="utf-8") as mf:
            json.dump(meta, mf, indent=4, ensure_ascii=False)
        return {
            "platform": "twitter",
            "username": username,
            "timestamp": now.strftime("%Y-%m-%d %H:%M:%S"),
            "is_premium": is_premium,
            "metrics": metrics
        }
    except Exception as e:
        import logging
        logging.error(f"Twitter istatistik çekme hatası: {str(e)}")
        return None
    finally:
        driver.quit()


# ----------------------------------------------------------------------------------
# İSTATİSTİK SİSTEMİ YARDIMCI FONKSİYONLARI
# ----------------------------------------------------------------------------------
def format_instagram_stats_for_display(stats: dict) -> str:
    """
    Instagram istatistik verilerini 2'li kutu formatında HTML döndürür.
    Twitter formatıyla birebir aynı yapıdadır. Değişim yüzdesi içermez.
    """
    html = '<div class="stats-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 6px;">\n'

    for key, value in stats.items():
        label = key.replace('_', ' ').capitalize()
        html += f'''
        <div class="stat-box">
            <div class="stat-label">{label}</div>
        </div>
        <div class="stat-box">
            <div class="stat-value">{value}</div>
        </div>
        '''

    html += '\n</div>'
    return html


def format_twitter_stats_for_display(stats_data):
    """
    Twitter istatistiklerini görüntülemek için formatlar
    """
    if not stats_data or "metrics" not in stats_data:
        return "İstatistik verisi bulunamadı."

    metrics = stats_data["metrics"]
    username = stats_data["username"]
    timestamp = stats_data["timestamp"]
    is_premium = stats_data.get("is_premium", False)

    formatted = f"Twitter İstatistikleri: @{username}<br>"
    formatted += f"Son güncelleme: {timestamp}<br><br>"

    # 7D verileri
    formatted += "<strong>Son 7 Günün İstatistiği:</strong><br>"
    formatted += "=" * 40 + "<br>"

    # Tüm metrikleri sıralı şekilde ekleyelim
    priority_metrics = ["Impressions", "Engagement rate", "Engagements", "Profile visits",
                        "Replies", "Likes", "Reposts", "Bookmarks", "Shares"]

    for label in priority_metrics:
        if label in metrics:
            metric_data = metrics[label]
            value = metric_data.get("value", "N/A")
            trend = metric_data.get("trend", "")
            formatted += f"{label:15s}: {value}"
            if trend:
                formatted += f", change: {trend}"
            formatted += "<br>"

    formatted += "=" * 40 + "<br><br>"

    # 4W verileri (premium ise)
    if is_premium and "4W" in metrics:
        formatted += "<strong>Son 4 Hafta İstatistiği:</strong><br>"
        formatted += "=" * 40 + "<br>"

        # 4W için de aynı metrikleri gösteriyoruz
        for label in priority_metrics:
            if label in metrics["4W"]:
                metric_data = metrics["4W"][label]
                value = metric_data.get("value", "N/A")
                trend = metric_data.get("trend", "")
                formatted += f"{label:15s}: {value}"
                if trend:
                    formatted += f", change: {trend}"
                formatted += "<br>"

        formatted += "=" * 40 + "<br>"

    return formatted


def format_twitter_stats_as_boxes(stats_data):
    username = stats_data.get("username", "kullanıcı")
    timestamp = stats_data.get("timestamp", "")
    metrics = stats_data.get("metrics", {})

    base_box_style = (
        "background-color:#2A2A2A;"
        "border:1px solid #373737;"
        "border-radius:6px;"
        "padding:10px;"
        "font-size:12px;"
        "color:#FFFFFF;"
        "text-align:center;"
        "display:flex;"
        "flex-direction:column;"
        "align-items:center;"
        "min-height:60px;"
    )

    html = f'<div style="font-family:Segoe UI; background-color:#1E1E1E; padding:8px;">'
    try:
        with open("C:/Users/<USER>/PycharmProjects/Sorcerio/x.png", "rb") as img_f:
            b64_logo = base64.b64encode(img_f.read()).decode()
        twitter_logo_html = (
            f'<img src="data:image/png;base64,{b64_logo}" '
            f'style="height:36px; width:auto; vertical-align:middle; margin-right:6px;" />'
        )
    except:
        twitter_logo_html = ""

    html += (
        f'<div style="display:flex; align-items:center; gap:6px; '
        f'font-size:16px; font-weight:bold; color:#FFFFFF; margin-bottom:10px; '
        f'letter-spacing:0.5px;">'
        f'{twitter_logo_html}'
        f'<span style="font-size:16px; letter-spacing:0.6px; margin-left:-1px;">{username}</span>'
        f'</div>'
    )

    def render_metric_stack(title, metric_data):
        section = (
            f'<div style="margin-top:12px; font-size:13px; '
            f'font-weight:bold; color:#FFFFFF; text-align:center;">{title}</div>'
        )
        section += '<div style="display:flex; flex-direction:column; gap:4px; margin-top:6px;">'
        for label, values in metric_data.items():
            if not isinstance(values, dict):
                continue
            value = values.get("value", "N/A")
            percent = values.get("percent", "")
            direction = values.get("direction", "")

            trend_color = (
                "#4CAF50" if direction == "up"
                else "#F44336" if direction == "down"
                else "#AAAAAA"
            )
            percent_html = (
                f'<div style="margin-top:6px; color:{trend_color}; '
                f'font-weight:bold; font-size:14px; text-align:center;">'
                f'{percent}'
                f'</div>'
            ) if percent else ""

            section += (
                f'<div style="{base_box_style}">'
                f'<div style="font-size:15px; font-weight:600; color:#DDDDDD; '
                f'margin-bottom:4px; text-align:center;">{label}</div>'
                f'<div style="font-size:22px; font-weight:700; color:#FFFFFF; '
                f'text-align:center;">{value}</div>'
                f'{percent_html}'
                f'</div>'
            )
        section += '</div>'
        return section

    metrics_7d = {k: v for k, v in metrics.items() if k != "4W"}
    if metrics_7d:
        html += render_metric_stack("Son 7 Günün İstatistiği", metrics_7d)

    if "4W" in metrics:
        html += render_metric_stack("Son 4 Haftanın İstatistiği", metrics["4W"])

    html += "</div>"
    return html


def stats_worker():
    """
    Arka planda çalışan, istatistik çekme işlemlerini gerçekleştiren worker thread
    """
    global stats_system
    while True:
        try:
            profile_data = stats_system.stats_queue.get(timeout=1)
            stats_system.is_processing = True
            platform = profile_data["platform"]
            username = profile_data["username"]
            password = profile_data["password"]
            logging.info(f"{platform.capitalize()} istatistikleri çekiliyor: {username}")

            if platform == "instagram":
                stats = extract_instagram_metrics(username, password)
            elif platform == "twitter":
                stats = extract_twitter_metrics(username, password)
            else:
                stats = None
                logging.error(f"Bilinmeyen platform: {platform}")

            if stats:
                profile_key = f"{platform}_{username}"
                stats_system.stats_data[profile_key] = stats
                stats_system.last_stats_update[profile_key] = datetime.now()
                if profile_key not in stats_system.stats_display_queue:
                    stats_system.stats_display_queue.append(profile_key)
                logging.info(f"{platform.capitalize()} istatistikleri başarıyla güncellendi: {username}")
                # Her yeni veri geldiğinde istatistikleri dosyaya kaydet
                stats_system.istatistikleri_kaydet()

            stats_system.stats_queue.task_done()
            stats_system.profiles_to_process.discard(f"{platform}_{username}")

        except queue.Empty:
            stats_system.is_processing = False
            time.sleep(1)
        except Exception as e:
            logging.error(f"İstatistik worker'da hata: {str(e)}")
            stats_system.is_processing = False
            time.sleep(5)


def check_profiles(force=False):
    """
    Profilleri kontrol eder ve gerekirse istatistik çekme kuyruğuna ekler.
    Ayrıca, 'downloaded' listesindeki fiziksel olarak silinmiş veya duplicate dosyaları da temizler.
    Her profilin istatistiği en son çekimden itibaren 24 saatte bir güncellenir.

    Program açıldığında tüm mevcut profilleri hemen tespit eder ve:
    - 24 saatten taze istatistikleri varsa onları gösterir
    - 24 saati geçmiş istatistikleri varsa yeniden çekip günceller

    Ayrıca, sistemde kayıtlı olup fiziksel olarak var olmayan profillerin istatistiklerini de temizler.
    """
    global stats_system
    try:
        now = datetime.now()
        # force True ise zaman kontrolü atlanır, her durumda çalışır
        is_startup = (now - stats_system.last_profile_check).total_seconds() > 3600
        regular_check = (now - stats_system.last_profile_check).total_seconds() >= 120

        if not force and not is_startup and not regular_check:
            return

        logging.info("Profiller kontrol ediliyor...")
        stats_system.last_profile_check = now

        base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
        config_dir = os.path.join(base_dir, "configuration")

        # Mevcut profilleri takip etmek için set kullan
        mevcut_profiller = set()

        platforms = ["instagram", "twitter"]
        for platform in platforms:
            platform_dir = os.path.join(config_dir, platform)
            if not os.path.exists(platform_dir):
                continue
            profile_files = [f for f in os.listdir(platform_dir) if f.endswith(".json")]
            for profile_file in profile_files:
                profile_path = os.path.join(platform_dir, profile_file)
                try:
                    # Profil dosyasını oku, bozuksa varsayılan oluştur
                    with open(profile_path, "r", encoding="utf-8") as f:
                        try:
                            profile_data = json.load(f)
                        except json.JSONDecodeError:
                            logging.warning(f"Bozuk JSON: {profile_path}. Yeni, varsayılan profil oluşturuluyor.")
                            profile_data = {
                                "username": "",
                                "password": "",
                                "schedule": {
                                    "monday": [], "tuesday": [], "wednesday": [], "thursday": [],
                                    "friday": [], "saturday": [], "sunday": []
                                },
                                "hashtags": [],
                                "links": [],
                                "downloaded": []
                            }
                            with open(profile_path, "w", encoding="utf-8") as fw:
                                json.dump(profile_data, fw, indent=4, ensure_ascii=False)

                    # Temizlik: Silinmiş dosyaları ve duplicate kayıtları ayıkla
                    original_downloaded = profile_data.get("downloaded", [])
                    seen_paths = set()
                    seen_urls = set()
                    cleaned_downloaded = []
                    for item in original_downloaded:
                        fpath = item.get("file_path", "")
                        url = item.get("url", "")
                        caption = item.get("caption", "")
                        unique_key = f"{fpath}|{url}|{caption}"

                        if not os.path.exists(fpath):
                            continue  # fiziksel dosya yoksa atla
                        if fpath in seen_paths or url in seen_urls:
                            continue  # duplicate dosya veya URL varsa atla
                        seen_paths.add(fpath)
                        seen_urls.add(url)
                        cleaned_downloaded.append(item)

                    if len(cleaned_downloaded) < len(original_downloaded):
                        profile_data["downloaded"] = cleaned_downloaded
                        with open(profile_path, "w", encoding="utf-8") as f:
                            json.dump(profile_data, f, indent=4, ensure_ascii=False)
                        logging.info(f"{profile_file} için silinmiş veya duplicate medya kayıtları temizlendi.")

                    username = profile_data.get("username", "").strip()
                    password = profile_data.get("password", "").strip()
                    if username and password:
                        profile_key = f"{platform}_{username}"
                        # Mevcut profil listesine ekle
                        mevcut_profiller.add(profile_key)

                        if profile_key in stats_system.profiles_to_process:
                            continue  # Zaten işleniyor

                        last_update = stats_system.last_stats_update.get(profile_key)
                        # İstatistik varsa ve 24 saat içindeyse, mevcut veriyi kullan
                        if last_update and (now - last_update).total_seconds() < 86400:
                            # Profil zaten istatistik display queue'da yoksa ekle
                            if profile_key not in stats_system.stats_display_queue:
                                stats_system.stats_display_queue.append(profile_key)
                            continue

                        # 24 saatten eski veya hiç veri yoksa, hemen çek
                        stats_system.stats_queue.put({
                            "platform": platform,
                            "username": username,
                            "password": password
                        })
                        stats_system.profiles_to_process.add(profile_key)
                        logging.info(f"İstatistik çekme kuyruğuna eklendi: {platform}/{username}")
                except Exception as e:
                    logging.error(f"Profil dosyası okunurken hata ({profile_file}): {str(e)}")

        # Var olmayan profillerin istatistiklerini temizle
        silinecek_istatistikler = set()

        # stats_data içindeki kayıtları kontrol et
        for profile_key in list(stats_system.stats_data.keys()):
            if profile_key not in mevcut_profiller:
                silinecek_istatistikler.add(profile_key)

        # stats_display_queue içindeki kayıtları kontrol et
        for profile_key in list(stats_system.stats_display_queue):
            if profile_key not in mevcut_profiller:
                stats_system.stats_display_queue.remove(profile_key)
                silinecek_istatistikler.add(profile_key)

        # last_stats_update içindeki kayıtları kontrol et
        for profile_key in list(stats_system.last_stats_update.keys()):
            if profile_key not in mevcut_profiller:
                silinecek_istatistikler.add(profile_key)

        # Var olmayan profillerin istatistiklerini sil
        for profile_key in silinecek_istatistikler:
            if profile_key in stats_system.stats_data:
                del stats_system.stats_data[profile_key]
            if profile_key in stats_system.last_stats_update:
                del stats_system.last_stats_update[profile_key]
            if profile_key in stats_system.profiles_to_process:
                stats_system.profiles_to_process.discard(profile_key)
            logging.info(f"Var olmayan profile ait istatistik temizlendi: {profile_key}")

        # Eğer silinen istatistik varsa dosyayı güncelle
        if silinecek_istatistikler:
            stats_system.istatistikleri_kaydet()

            # Eğer şu anda gösterilen profil silindiyse, başka bir profile geç
            if stats_system.current_displayed_profile in silinecek_istatistikler:
                stats_system.current_displayed_profile = next(iter(stats_system.stats_display_queue), None)

    except Exception as e:
        logging.error(f"Profilleri kontrol ederken hata: {str(e)}")


def update_stats_display_html() -> str:
    try:
        base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio\\configuration"
        all_profiles = []
        # Var olan profilleri takip etmek için set kullan
        mevcut_profiller = set()

        for platform in ["instagram", "twitter"]:
            platform_dir = os.path.join(base_dir, platform)
            if not os.path.exists(platform_dir):
                continue
            for file in os.listdir(platform_dir):
                if file.endswith(".json"):
                    profile_path = os.path.join(platform_dir, file)
                    try:
                        with open(profile_path, "r", encoding="utf-8") as f:
                            try:
                                data = json.load(f)
                            except json.JSONDecodeError:
                                logging.warning(f"Bozuk JSON: {profile_path}. Varsayılan yapı kaydediliyor.")
                                data = {
                                    "username": "",
                                    "password": "",
                                    "schedule": {
                                        "monday": [], "tuesday": [], "wednesday": [], "thursday": [],
                                        "friday": [], "saturday": [], "sunday": []
                                    },
                                    "hashtags": [],
                                    "links": [],
                                    "downloaded": []
                                }
                                with open(profile_path, "w", encoding="utf-8") as fw:
                                    json.dump(data, fw, indent=4, ensure_ascii=False)
                        username = data.get("username", "").strip()
                        if not username:
                            continue

                        # Mevcut profiller listesine ekle
                        profile_key = f"{platform}_{username}"
                        mevcut_profiller.add(profile_key)

                        links = data.get("links", [])
                        downloaded_urls = {item["url"] for item in data.get("downloaded", []) if
                                           isinstance(item, dict) and "url" in item}
                        if not links and data.get("downloaded"):
                            link_count = len(data["downloaded"])
                        else:
                            link_count = len([link for link in links if (
                                link if isinstance(link, str) else link.get("url", "")) not in downloaded_urls])
                        # İstatistik sistemine de link sayısını yaz
                        profile_key = f"{platform}_{username}"
                        if profile_key in stats_system.stats_data:
                            stats_system.stats_data[profile_key]["link_count"] = link_count
                        all_profiles.append({
                            "platform": platform,
                            "username": username,
                            "link_count": link_count
                        })
                    except Exception as e:
                        logging.warning(f"JSON okuma hatası: {file} - {str(e)}")

        # stats_system içindeki verileri temizle
        silinecek_istatistikler = set()
        for profile_key in list(stats_system.stats_data.keys()):
            if profile_key not in mevcut_profiller:
                silinecek_istatistikler.add(profile_key)

        for profile_key in list(stats_system.stats_display_queue):
            if profile_key not in mevcut_profiller:
                stats_system.stats_display_queue.remove(profile_key)
                silinecek_istatistikler.add(profile_key)

        for profile_key in silinecek_istatistikler:
            if profile_key in stats_system.stats_data:
                del stats_system.stats_data[profile_key]
            if profile_key in stats_system.last_stats_update:
                del stats_system.last_stats_update[profile_key]
            if profile_key == stats_system.current_displayed_profile:
                stats_system.current_displayed_profile = next(iter(stats_system.stats_display_queue), None)
            logging.info(f"HTML güncelleme sırasında var olmayan profile ait istatistik temizlendi: {profile_key}")

        # Eğer silinen istatistik varsa dosyayı güncelle
        if silinecek_istatistikler:
            stats_system.istatistikleri_kaydet()

        if not all_profiles:
            return '''
            <div class="skeleton-container" id="extra-skeleton">
                <div class="skeleton" style="height: 20px; width: 70%;"></div>
                <div class="skeleton" style="height: 14px; width: 100%;"></div>
                <div class="skeleton" style="height: 14px; width: 90%;"></div>
                <div class="skeleton" style="height: 14px; width: 80%;"></div>
            </div>
            '''
        all_profiles.sort(key=lambda x: x["link_count"])
        html_blocks = []
        for prof in all_profiles:
            link_count = int(prof["link_count"])
            percent = min(100, link_count)
            if link_count == 0:
                percent = 100
                color = "#FF0000"
            elif percent > 80:
                color = "#4CAF50"
            elif percent > 60:
                color = "#8BC34A"
            elif percent > 40:
                color = "#FFC107"
            elif percent > 20:
                color = "#FF9800"
            else:
                color = "#F44336"
            logo_file = "x.png" if prof["platform"] == "twitter" else "instagram.png"
            full_img_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), logo_file)
            try:
                with open(full_img_path, "rb") as img_f:
                    b64 = base64.b64encode(img_f.read()).decode()
                img_src = f"data:image/png;base64,{b64}"
            except Exception as e:
                logging.warning(f"Logo yüklenemedi: {logo_file} - {str(e)}")
                img_src = ""
            html_blocks.append(f"""
                <div class=\"bar-entry\" style=\"margin-bottom:12px; transition: all 0.4s ease-in-out;\">
                    <div style=\"display:flex; align-items:center; gap:10px; margin-bottom:6px;\">
                        <img src=\"{img_src}\" style=\"height:32px; width:auto;\" />
                        <span style=\"font-size:16px; font-weight:bold; color:#FFFFFF; letter-spacing:0.6px; margin-left:-1px;\">{prof["username"]}</span>
                    </div>
                    <div style=\"background:#333; border-radius:8px; height:14px; width:100%; overflow:hidden;\">
                        <div style=\"background:{color}; width:{percent}%; height:100%; border-radius:8px; transition:width 0.4s ease;\"></div>
                    </div>
                    <div style=\"font-size:13px; font-weight:bold; letter-spacing:1px; color:#FFFFFF; text-align:right; padding-right:4px;\">
                        {prof["link_count"]} link
                    </div>
                </div>
            """)
        full_html = f'''
            <div class="bar-container" style="display:flex; flex-direction:column; gap:10px; transition: all 0.4s ease-in-out;">
                {''.join(html_blocks)}
            </div>
        '''
        return full_html
    except Exception as e:
        logging.error(f"Can barları güncellenirken hata: {str(e)}")
        return ""


def initialize_stats_system():
    global stats_system
    if stats_system.initialized:
        return
    try:
        logging.info("İstatistik sistemi başlatılıyor...")
        stats_system.istatistikleri_yukle()

        if stats_system.stats_data:
            # Yüklenen istatistikler varsa, ilk uygun profilin detaylarını göstermek üzere ayarla
            # Öncelik stats_display_queue'deki ilk elemana verilebilir veya stats_data'dan ilk anahtar alınabilir.
            # Şimdilik stats_data'dan ilk anahtarı alıyoruz.

            # stats_data'daki anahtarları sıralı almak tutarlılık sağlar
            ordered_profile_keys = sorted(list(stats_system.stats_data.keys()))
            first_profile_key = next(iter(ordered_profile_keys), None)

            if first_profile_key:
                stats_system.current_displayed_profile = first_profile_key
                # Eğer stats_display_queue kullanılıyorsa ve bu profil listede yoksa,
                # gösterim döngüsüne dahil olması için ekleyelim (en başa ekleyerek hemen gösterilmesini sağlayabiliriz)
                if first_profile_key not in stats_system.stats_display_queue:
                    stats_system.stats_display_queue.appendleft(first_profile_key)
                logging.info(f"Başlangıçta görüntülenecek istatistik profili ayarlandı: {first_profile_key}")
            else:
                # stats_data dolu ama geçerli bir anahtar bulunamadı (normalde olmamalı)
                stats_system.current_displayed_profile = None
        else:
            # Yüklenecek istatistik verisi bulunamadı
            stats_system.current_displayed_profile = None

        stats_system.worker_thread = threading.Thread(target=stats_worker, daemon=True)
        stats_system.worker_thread.start()
        check_profiles()
        # update_stats_display_html() # Bu çağrı doğrudan ana UI'yi güncellemez, QMainWindow içindeki self.update_stats() yapar.
        stats_system.initialized = True
        logging.info("İstatistik sistemi başarıyla başlatıldı.")
    except Exception as e:
        logging.error(f"İstatistik sistemi başlatılırken hata: {e}")
        stats_system.current_displayed_profile = None  # Hata durumunda da gösterilecek profil olmasın


def create_directory(path):
    """İndirilen dosyaların kaydedileceği dizini oluşturur"""
    if not os.path.exists(path):
        os.makedirs(path)
        logging.info(f"Dizin oluşturuldu: {path}")


def clean_temp_files(temp_dir):
    """Geçici dosyaları temizler"""
    try:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            logging.info(f"Geçici dosyalar temizlendi: {temp_dir}")
    except Exception as e:
        logging.error(f"Geçici dosyalar temizlenirken hata oluştu: {str(e)}")


def sanitize_filename(filename):
    """Dosya adını güvenli hale getirir"""
    if not filename:
        return None
    filename = filename.replace("#", "")  # '#' karakterini doğrudan temizle
    filename = re.sub(r'[<>:"/\\|?*]', "", filename)
    filename = re.sub(r"[^\w\s-]", "", filename)
    filename = re.sub(r"\s+", "-", filename.strip())
    filename = filename[:240]
    if not filename:
        return None
    return filename


def download_instagram_post(url, output_folder="instagramdownloaded"):
    shortcode = extract_shortcode_from_url(url)
    if not shortcode:
        logging.error("Geçersiz Instagram bağlantısı.")
        return False, None, None, None  # 4 değer döndür

    temp_folder = "temp_instagram_download"
    if os.path.exists(temp_folder):
        shutil.rmtree(temp_folder)
    os.makedirs(temp_folder, exist_ok=True)

    loader = instaloader.Instaloader(
        download_video_thumbnails=False,
        download_comments=False,
        save_metadata=False,
        compress_json=False,
        dirname_pattern=temp_folder,
        filename_pattern="{shortcode}"
    )

    try:
        post = instaloader.Post.from_shortcode(loader.context, shortcode)
        raw_caption = post.caption or post.shortcode
        cleaned_caption = re.sub(r'#\w+', '', raw_caption).strip()
        short_caption = " ".join(re.findall(r'\w+', cleaned_caption)[:2])
        caption = sanitize_filename(short_caption)
        loader.download_post(post, target=".")

        for file in os.listdir(temp_folder):
            if file.endswith((".mp4", ".jpg", ".png")):
                ext = os.path.splitext(file)[1]
                new_name = f"{caption}{ext}"
                final_path = os.path.join(output_folder, new_name)
                shutil.move(os.path.join(temp_folder, file), final_path)
                shutil.rmtree(temp_folder)
                return True, caption, os.path.normpath(final_path), raw_caption  # 4 değer

        shutil.rmtree(temp_folder)
        logging.warning(f"Medya dosyası bulunamadı: {url}")
        return False, None, None, None  # 4 değer

    except Exception as e:
        logging.error(f"İndirme hatası: {e}")
        return False, None, None, None  # 4 değer


# ---------- Instagram Video İndirme Fonksiyonları ----------
def get_caption_and_shortcode(post):
    """Instagram gönderisinin başlığını veya shortcode'unu alır"""
    try:
        caption = post.caption if post.caption else ""
        caption = caption.encode("ascii", "ignore").decode("ascii")
        caption = caption[:100]
        safe_caption = sanitize_filename(caption)
        if not safe_caption:
            return post.shortcode
        return safe_caption
    except Exception as e:
        logging.error(f"Başlık alınırken hata oluştu: {str(e)}")
        return post.shortcode


def extract_shortcode_from_url(url):
    """
    URL'den shortcode'u çıkarır.
    Eğer URL '/status/' içeriyorsa, bu string'den sonraki kısmı döndürür;
    aksi halde, URL'nin son kısmını döndürür.
    """
    try:
        url = url.strip().split("?")[0].rstrip("/")
        if "/status/" in url:
            return url.split("/status/")[-1]
        else:
            return url.split("/")[-1]
    except Exception as e:
        logging.error(f"URL'den shortcode çıkarılırken hata oluştu: {url} - Hata: {str(e)}")
        return None


def move_video_to_destination(temp_dir, output_dir, filename):
    """İndirilen videoyu hedef klasöre taşır ve yeni yolu döner"""
    try:
        video_files = [f for f in os.listdir(temp_dir) if f.endswith((".mp4", ".webm"))]
        if video_files:
            old_path = os.path.join(temp_dir, video_files[0])
            new_path = os.path.join(output_dir, f"{filename}.mp4")
            counter = 1
            base_path = new_path[:-4]
            while os.path.exists(new_path):
                new_path = f"{base_path}-{counter}.mp4"
                counter += 1
            shutil.move(old_path, new_path)
            logging.info(f"Video başarıyla taşındı: {new_path}")
            return True, new_path
    except Exception as e:
        logging.error(f"Video taşınırken hata oluştu: {str(e)}")
    return False, None


def download_instagram_video(L, shortcode, temp_dir, output_dir):
    """Instagram gönderisini indirir ve (başarı, caption, dosya_yolu) şeklinde döner"""
    try:
        clean_temp_files(temp_dir)
        create_directory(temp_dir)
        post = instaloader.Post.from_shortcode(L.context, shortcode)
        caption = get_caption_and_shortcode(post)
        L.download_post(post, target=temp_dir)

        if post.is_video:
            success, path = move_video_to_destination(temp_dir, output_dir, caption)
            clean_temp_files(temp_dir)
            return success, caption, path


        elif post.typename in ["GraphImage", "GraphSidecar"]:
            image_files = [f for f in os.listdir(temp_dir) if f.lower().endswith((".jpg", ".jpeg", ".png"))]
            if image_files:
                old_path = os.path.join(temp_dir, image_files[0])
                new_path = os.path.join(output_dir, f"{caption}.jpg")
                counter = 1
                base_path = new_path[:-4]
                while os.path.exists(new_path):
                    new_path = f"{base_path}-{counter}.jpg"
                    counter += 1
                shutil.move(old_path, new_path)
                clean_temp_files(temp_dir)
                logging.info(f"Instagram görseli indirildi: {new_path}")
                return True, caption, new_path

        logging.warning(f"Bu gönderi ne video ne de tekli görsel: {shortcode}")
        clean_temp_files(temp_dir)
        return False, None, None

    except Exception as e:
        logging.error(f"Instagram gönderi indirimi sırasında hata ({shortcode}): {str(e)}")
        return False, None, None


# ---------- YouTube Video İndirme Fonksiyonları ----------
def is_shorts_url(url):
    """URL'nin YouTube Shorts olup olmadığını kontrol eder"""
    return "/shorts/" in url.lower()


def convert_shorts_to_normal_url(url):
    """Shorts URL'sini normal YouTube URL'sine çevirir"""
    if is_shorts_url(url):
        video_id = url.split("/shorts/")[1].split("?")[0]
        return f"https://www.youtube.com/watch?v={video_id}"
    return url


def download_youtube_video(url, output_dir, ffmpeg_dir):
    """
    YouTube videosunu indirir (MP4 formatında).
    """
    try:
        normalized_url = convert_shorts_to_normal_url(url)
        ydl_opts = {
            "format": "(bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4])/(bestvideo+bestaudio/best)",
            "outtmpl": os.path.join(output_dir, "%(title)s.%(ext)s"),
            "restrictfilenames": True,
            "noplaylist": True,
            "nocheckcertificate": True,
            "ignoreerrors": False,
            "no_warnings": True,
            "quiet": True,
            "ffmpeg_location": ffmpeg_dir,
            "merge_output_format": "mp4",
            "recodevideo": "mp4",
            "ffmpeg_args": ["-crf", "18", "-preset", "fast"],
        }
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(normalized_url, download=False)
            video_title = info.get("title", None)
            if video_title:
                safe_title = sanitize_filename(video_title)
                if safe_title:
                    ydl.download([normalized_url])
                    video_type = "Shorts" if is_shorts_url(url) else "Video"
                    logging.info(f"YouTube {video_type} başarıyla indirildi: {safe_title}")
                    return True
            logging.error(f"Video başlığı alınamadı: {url}")
            return False
    except Exception as e:
        logging.error(f"YouTube videosu indirilirken hata oluştu ({url}): {str(e)}")
        return False


def download_twitter_media(url, output_dir, ffmpeg_dir):
    """
    Twitter bağlantısından video veya görsel indirir.
    Başarı durumunda: (success: bool, caption: str, file_path: str)
    Hata durumunda: (False, None, None)
    """
    try:
        if "x.com/" in url:
            url = url.replace("x.com", "twitter.com")

        os.makedirs(output_dir, exist_ok=True)

        with yt_dlp.YoutubeDL({"quiet": True}) as ydl:
            info = ydl.extract_info(url, download=False)
            raw_caption = info.get("title", "").strip()
            cleaned_caption = re.sub(r'#\w+', '', raw_caption).strip()
            safe_title = sanitize_filename(cleaned_caption)

            if not safe_title:
                logging.error(f"Tweet başlığı alınamadı: {url}")
                return False, None, None  # 3 değer dönmeli

            is_video = any(f.get('vcodec') != 'none' for f in info.get('formats', []))
            is_image = info.get('ext') in ['jpg', 'png'] or (info.get('_type') == 'entry_list' and any(
                e.get('ext') in ['jpg', 'png'] for e in info.get('entries', [])))

            ydl_opts = {
                "outtmpl": os.path.join(output_dir, f"{safe_title}.%(ext)s"),
                "restrictfilenames": True,
                "quiet": True,
            }

            if is_video:
                ydl_opts["format"] = "(bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4])"
                ydl_opts["ffmpeg_location"] = ffmpeg_dir
                ydl_opts["merge_output_format"] = "mp4"
            elif is_image:
                ydl_opts["format"] = "best"
            else:
                logging.warning(f"Desteklenmeyen medya türü: {url}")
                return False, None, None  # 3 değer

            with yt_dlp.YoutubeDL(ydl_opts) as ydl2:
                ydl2.download([url])

            expected_ext = ['.mp4'] if is_video else ['.jpg', '.png']
            downloaded_files = [f for f in os.listdir(output_dir)
                                if f.startswith(safe_title)
                                and os.path.splitext(f)[1].lower() in expected_ext]

            if downloaded_files:
                file_path = os.path.join(output_dir, downloaded_files[0])
                logging.info(f"Twitter medyası indirildi: {file_path}")
                return True, safe_title, file_path  # 3 değer
            else:
                logging.error(f"İndirilen dosya bulunamadı: {url}")
                return False, None, None  # 3 değer

    except Exception as e:
        logging.error(f"Twitter indirme hatası: {str(e)}")
        return False, None, None  # 3 değer


def create_new_driver_for_account(account_username):
    import undetected_chromedriver as uc

    options = uc.ChromeOptions()
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    unique_profile_path = os.path.join(base_dir, f"chrome_profile_{account_username}")
    os.makedirs(unique_profile_path, exist_ok=True)
    options.add_argument(f'--user-data-dir={unique_profile_path}')
    options.add_argument('--headless=new')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-notifications')
    options.add_argument('--window-size=1920,1080')
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) ' +
                         'AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')

    driver = uc.Chrome(options=options, version_main=135)
    return driver


def login_twitter_account(driver, username, password):
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    import time

    try:
        logging.info("Twitter'a giriş yapılıyor...")
        driver.get("https://x.com/i/flow/login")
        time.sleep(5)
        driver.execute_script("window.localStorage.clear();")
        driver.execute_script("window.sessionStorage.clear();")
        # Tüm çerezleri silmek için:
        driver.execute_script("""
            document.cookie.split(';').forEach(function(c) {
                document.cookie = c.trim().split('=')[0] + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC;';
            });
        """)

        user_input = WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.XPATH, '//input[@autocomplete="username"]'))
        )
        user_input.clear()
        user_input.send_keys(username)
        time.sleep(1)
        next_button = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable(
                (By.XPATH, "//span[text()='Next']/ancestor::button | //span[text()='İleri']/ancestor::button"))
        )
        next_button.click()
        time.sleep(5)

        password_input = None
        password_xpaths = [
            '//input[@autocomplete="current-password"]',
            '//input[@name="password"]',
            '//input[@type="password"]'
        ]
        for xpath in password_xpaths:
            try:
                password_input = WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.XPATH, xpath))
                )
                if password_input:
                    logging.info(f"Şifre alanı bulundu: {xpath}")
                    break
            except Exception:
                continue

        if not password_input:
            logging.error("Şifre giriş alanı bulunamadı.")
            return False

        password_input.clear()
        password_input.send_keys(password)
        time.sleep(1)
        login_button = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable((By.XPATH, "//button[@data-testid='LoginForm_Login_Button']"))
        )
        driver.execute_script("arguments[0].click();", login_button)
        time.sleep(10)
        logging.info(f"{username} ile giriş denemesi tamamlandı.")
        return True
    except Exception as e:
        logging.error(f"{username} ile giriş hatası: {str(e)}")
        return False


# ----------------------------------------------------------------------
# ARTIK LINK KAYNAĞI OLARAK PROFİLLERİN .JSON DOSYASI KULLANILACAK
# ----------------------------------------------------------------------

def process_instagram_downloads_from_profiles(instagram_output_dir, temp_dir):
    """
    Instagram için 'configuration/instagram' klasöründeki tüm profil .json dosyalarındaki
    linkleri indirir. Hem Instagram hem Twitter linklerini işler ve indirilen her medya
    dosyasının yolunu ve açıklamasını ilgili profil JSON'una kaydeder.
    """
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    config_dir = os.path.join(base_dir, "configuration", "instagram")
    ffmpeg_dir = download_ffmpeg()

    L = instaloader.Instaloader(
        download_videos=True,
        download_video_thumbnails=False,
        download_geotags=False,
        download_comments=False,
        save_metadata=False,
        post_metadata_txt_pattern="",
        dirname_pattern=temp_dir,
        filename_pattern="{shortcode}",
        quiet=False,
        compress_json=False,
        fatal_status_codes=[400, 429],
    )

    if not os.path.exists(config_dir):
        logging.info(f"Instagram konfigürasyon klasörü bulunamadı: {config_dir}")
        return

    profile_files = [f for f in os.listdir(config_dir) if f.endswith(".json")]
    logging.info(f"Toplam {len(profile_files)} Instagram profili bulundu.")

    for pfile in profile_files:
        profile_path = os.path.join(config_dir, pfile)
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            links = data.get("links", [])
            downloaded_urls = {
                item["url"]
                for item in data.get("downloaded", [])
                if isinstance(item, dict) and "url" in item
            }
            links = [link for link in links if link not in downloaded_urls]
            if not links:
                logging.info(f"Bu profilde indirilecek yeni link yok: {pfile}")
                continue

            # İndirilenleri kaydedeceğimiz liste
            downloaded = data.get("downloaded", [])

            logging.info(f"--- Profil: {pfile}, Link sayısı: {len(links)} ---")
            for i, url in enumerate(links, 1):
                try:
                    logging.info(f"İndiriliyor (Instagram Profili İçinde): {url} ({i}/{len(links)})")
                    success_download = False
                    caption = None
                    file_path = None
                    original_caption = None

                    if "twitter.com" in url or "x.com" in url:
                        success_download, caption, file_path = download_twitter_media(
                            url, instagram_output_dir, ffmpeg_dir
                        )
                    else:
                        success_download, caption, file_path, original_caption = download_instagram_post(
                            url, instagram_output_dir
                        )

                    if success_download and caption and file_path:
                        logging.info(f"✓ Medya indirildi: {file_path}")
                        downloaded.append({
                            "url": url,
                            "file_path": file_path,
                            "caption": caption,
                            "original_caption": original_caption
                        })
                        data["downloaded"] = downloaded
                        with open(profile_path, "w", encoding="utf-8") as f:
                            json.dump(data, f, indent=4, ensure_ascii=False)
                    else:
                        logging.error(f"Medya indirilemedi ({i}/{len(links)}): {url}")

                except Exception as e:
                    logging.error(f"Link işlenirken hata ({i}/{len(links)}): {url} -> {e}")

                time.sleep(3)

        except Exception as e:
            logging.error(f"Profil dosyası okunurken hata oluştu: {pfile} -> {e}")


def download_all_profiles_links():
    """
    Tüm platformlardaki tüm profillerin .json içindeki 'links' listesini indirir.
    """
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    videos_dir = os.path.join(base_dir, "videos")
    temp_dir = os.path.join(videos_dir, "temp")
    ffmpeg_dir = download_ffmpeg()

    # Çıktı klasörleri
    instagram_output_dir = os.path.join(videos_dir, "instagramdownloaded")
    twitter_output_dir = os.path.join(videos_dir, "twitterdownloaded")
    youtube_output_dir = os.path.join(videos_dir, "youtubedownloaded")

    create_directory(instagram_output_dir)
    create_directory(twitter_output_dir)
    create_directory(youtube_output_dir)
    create_directory(temp_dir)

    # Her platform için "downloads_from_profiles" fonksiyonunu çağır
    process_instagram_downloads_from_profiles(instagram_output_dir, temp_dir)
    process_twitter_downloads_from_profiles(twitter_output_dir, ffmpeg_dir)
    process_youtube_downloads_from_profiles(youtube_output_dir, ffmpeg_dir)


def schedule_all_profiles_uploads(scheduler):
    """
    Tüm platformlardaki .json dosyalarını okuyup
    her profilde tanımlı 'schedule' saatleri için APScheduler job'ları ekler.
    Saat geldiğinde 'upload_profile_videos(profile_path, platform)' fonksiyonunu tetikler.
    """
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    config_dir = os.path.join(base_dir, "configuration")

    # Hangi platformlara bakacağız?
    platforms = ["instagram", "twitter", "youtube"]

    # Gün isimlerini APScheduler cron formatına uyarlamak için bir sözlük:
    day_map = {
        "monday": "0",
        "tuesday": "1",
        "wednesday": "2",
        "thursday": "3",
        "friday": "4",
        "saturday": "5",
        "sunday": "6",
    }

    for platform in platforms:
        platform_dir = os.path.join(config_dir, platform)
        if not os.path.exists(platform_dir):
            continue

        # Tüm profil dosyalarını gez
        for profile_file in os.listdir(platform_dir):
            if not profile_file.endswith(".json"):
                continue
            profile_path = os.path.join(platform_dir, profile_file)
            try:
                with open(profile_path, "r", encoding="utf-8") as f:
                    profile_data = json.load(f)

                # schedule alanını okuyalım
                schedule_dict = profile_data.get("schedule", {})  # { "monday": ["10:00","14:30"], ... }

                # Her gün için tanımlanan saatleri APScheduler cron job olarak ekle
                for day_name, time_list in schedule_dict.items():
                    # Ör: "monday", ["10:00", "15:05"]
                    if day_name.lower() not in day_map:
                        continue  # Geçerli bir gün değilse atla
                    day_of_week_str = day_map[day_name.lower()]

                    for t_str in time_list:
                        # t_str = "HH:MM" formatında
                        try:
                            hour, minute = t_str.split(":")
                            hour = int(hour)
                            minute = int(minute)

                            # Job ekliyoruz: tam bu gün ve bu saatte -> upload_profile_videos(profile_path, platform)
                            scheduler.add_job(
                                upload_profile_videos,
                                'cron',
                                day_of_week=day_of_week_str,
                                hour=hour,
                                minute=minute,
                                args=[profile_path, platform],
                                id=f"{platform}_{profile_file}_{day_of_week_str}_{hour}_{minute}",
                                replace_existing=True
                            )
                            logging.info(f"Job eklendi: {platform}, {profile_file}, {day_name} {t_str}")
                        except ValueError as ve:
                            logging.error(f"Schedule parse error: {t_str} => {ve}")
            except Exception as e:
                logging.error(f"Profil dosyası okunurken hata: {profile_file} => {e}")


def upload_profile_videos(profile_path, platform):
    """
    Saat geldiğinde APScheduler tarafından çağrılır.
    Daha önce indirilen videoları 'upload_for_profile' fonksiyonları ile paylaşır.
    """
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    videos_dir = os.path.join(base_dir, "videos")

    if platform == "instagram":
        output_dir = os.path.join(videos_dir, "instagramdownloaded")
    elif platform == "twitter":
        output_dir = os.path.join(videos_dir, "twitterdownloaded")
    elif platform == "youtube":
        output_dir = os.path.join(videos_dir, "youtubedownloaded")
    else:
        logging.warning(f"Tanımsız platform: {platform}")
        return

    # Profil JSON'u aç
    try:
        with open(profile_path, "r", encoding="utf-8") as f:
            profile_data = json.load(f)
    except Exception as e:
        logging.error(f"upload_profile_videos: Profil dosyası açılamadı: {profile_path} => {e}")
        return

    # Şimdi 'upload_instagram_video_for_profile(...)' gibi fonksiyonları çağırabilirsiniz.
    if platform == "instagram":
        success = upload_instagram_video_for_profile(profile_path, profile_data, output_dir)
        if success:
            logging.info(f"Instagram profilinde upload tamam: {profile_path}")
    elif platform == "twitter":
        success = upload_twitter_video_for_profile(profile_data, output_dir)
        if success:
            logging.info(f"Twitter profilinde upload tamam: {profile_path}")
    elif platform == "youtube":
        # Burada youtube upload mantığı varsa
        pass


def process_profile_links(platform, output_dir, temp_dir, ffmpeg_dir):
    """
    İlgili platformun configuration klasöründeki tüm profilleri,
    JSON dosyasındaki "links" listesini işleyerek, indirme ve yükleme işlemlerini gerçekleştirir.
    İndirme başarılıysa, link nesnesini { "url": ..., "description": ... } olarak günceller.
    Yükleme başarılı ise, ilgili linki JSON'dan kaldırır.
    """
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    config_dir = os.path.join(base_dir, "configuration", platform)
    if not os.path.exists(config_dir):
        logging.info(f"{platform} konfigürasyon klasörü bulunamadı: {config_dir}")
        return

    profile_files = [f for f in os.listdir(config_dir) if f.endswith(".json")]
    for profile_file in profile_files:
        profile_path = os.path.join(config_dir, profile_file)
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                profile_data = json.load(f)
            links = profile_data.get("links", [])
            if not links:
                continue

            updated = False  # JSON dosyasında değişiklik yapıldı mı
            username = profile_data.get("username", "")  # stats güncellemesi için

            for idx, item in enumerate(links[:]):
                # item string veya dict olabilir
                if isinstance(item, dict):
                    url = item.get("url")
                elif isinstance(item, str):
                    url = item
                else:
                    logging.warning(f"Geçersiz link formatı atlandı: {item}")
                    continue

                if not url:
                    continue

                success_download = False
                caption = None
                success_upload = False

                if "instagram.com" in url:
                    shortcode = extract_shortcode_from_url(url)
                    if not shortcode:
                        logging.error(f"Geçersiz Instagram linki: {url}")
                        continue
                    L = instaloader.Instaloader(
                        download_videos=True,
                        download_video_thumbnails=False,
                        download_geotags=False,
                        download_comments=False,
                        save_metadata=False,
                        post_metadata_txt_pattern="",
                        dirname_pattern=temp_dir,
                        filename_pattern="{shortcode}",
                        quiet=True,
                        compress_json=False,
                        fatal_status_codes=[400, 429],
                    )
                    success_download, caption = download_instagram_video(L, shortcode, temp_dir, output_dir)
                    if success_download:
                        success_upload = upload_instagram_video_for_profile(profile_data, output_dir)

                elif "twitter.com" in url or "x.com" in url:
                    success_download, caption = download_twitter_media(url, output_dir, ffmpeg_dir)
                    if success_download:
                        success_upload = upload_twitter_video_for_profile(profile_data, output_dir)

                else:
                    logging.warning(f"Bilinmeyen platformdan gelen link atlandı: {url}")
                    continue

                if success_download and caption:
                    new_item = {"url": url, "description": caption}
                    links[idx] = new_item
                    updated = True
                    logging.info(f"{platform} linki için caption güncellendi: {url}")

                if success_download and success_upload:
                    links = [l for l in links if (l if isinstance(l, str) else l.get("url", "")) != url]
                    updated = True
                    logging.info(f"{platform} profilindeki link başarıyla işlendi ve kaldırıldı: {url}")

                    # Can bar sayısını azalt
                    profile_key = f"{platform}_{username}"
                    if profile_key in stats_system.stats_data:
                        stats_system.stats_data[profile_key]["link_count"] = max(0, stats_system.stats_data[
                            profile_key].get("link_count", 1) - 1)

                    # UI'da güncelle
                    update_stats_display()

                time.sleep(3)

            if updated:
                profile_data["links"] = links
                with open(profile_path, "w", encoding="utf-8") as f:
                    json.dump(profile_data, f, indent=4)

        except Exception as e:
            logging.error(f"Profil dosyası okunurken hata oluştu ({profile_file}): {str(e)}")


def upload_instagram_video_for_profile(profile_path, profile_data, instagram_output_dir):
    try:
        import json
        from datetime import datetime, timedelta
        import random
        import os
        import logging

        username = profile_data.get("username")
        password = profile_data.get("password")
        if not username or not password:
            logging.error("Instagram profilinde kullanıcı adı veya şifre eksik.")
            return False

        downloaded = profile_data.get("downloaded", [])
        downloaded = [item for item in downloaded if os.path.exists(item.get("file_path", ""))]
        profile_data["downloaded"] = downloaded

        # ? Temizlik sonrası JSON dosyasını güncelle
        with open(profile_path, "w", encoding="utf-8") as f:
            json.dump(profile_data, f, indent=4, ensure_ascii=False)
        update_stats_display_html()

        # ? Can barlarını anlık güncelle
        try:
            from PyQt5.QtWidgets import QApplication
            for widget in QApplication.topLevelWidgets():
                if hasattr(widget, "update_stats"):
                    widget.update_stats()
                    break
        except Exception as e:
            logging.warning(f"Can bar güncelleme hatası (instagram): {e}")
            update_stats_display_html()

        if not downloaded:
            logging.error("downloaded listesi boş.")
            return False

        first_item = downloaded[0]
        video_path = os.path.normpath(first_item.get("file_path"))
        caption = first_item.get("original_caption") or first_item.get("caption", "")
        thumbnail_path = os.path.splitext(video_path)[0] + ".jpg"

        def transliterate_tr(s: str) -> str:
            return s.translate(str.maketrans(
                "ğıüşöçİĞÜŞÖÇ",
                "giusocIGUSOC"
            ))

        original_video_path_before_rename = video_path
        base, ext = os.path.splitext(video_path)
        safe_base = transliterate_tr(base)
        if safe_base != base:
            new_video_path = safe_base + ext
            if not os.path.isabs(new_video_path):
                new_video_path = os.path.join(os.path.dirname(video_path), new_video_path)
            try:
                os.rename(video_path, new_video_path)
                video_path = new_video_path
                logging.info(f"Dosya adı transliterate edildi: {video_path}")
            except Exception as rename_err:
                logging.error(f"Dosya adı transliterate edilirken hata (orijinal dosya kullanılacak): {rename_err}")

        current_base_name = os.path.splitext(os.path.basename(video_path))[0]
        thumbnail_path = os.path.join(os.path.dirname(video_path), current_base_name + ".jpg")

        if not os.path.exists(thumbnail_path):
            try:
                vidcap = cv2.VideoCapture(video_path)
                success, frame = vidcap.read()
                attempt = 0
                while success and frame.mean() < 10 and attempt < 5:
                    success, frame = vidcap.read()
                    attempt += 1
                if success and frame.mean() >= 10:
                    cv2.imwrite(thumbnail_path, frame)
                    logging.info(f"Thumbnail oluşturuldu: {thumbnail_path}")
                else:
                    logging.warning(f"Geçerli kare bulunamadı: {video_path}")
                vidcap.release()
            except Exception as e:
                logging.error(f"Thumbnail üretim hatası: {e}")

        if not video_path or not os.path.exists(video_path):
            logging.error(f"Video dosyası bulunamadı: {video_path}")
            return False

        duration = dummy_duration(video_path)
        if duration < 1:
            logging.error("Video çok kısa (1 saniyeden az).")
            return False
        if duration > 60:
            logging.warning("Video 60 saniyeden uzun; Instagram tarafından reddedilebilir.")

        cl = Client()
        # Oturum dosyasını yükle veya yeniden giriş yap
        base_config_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio\\configuration\\instagram"
        session_dir = os.path.join(base_config_dir, "sessions")
        session_file = os.path.join(session_dir, f"{username}_session.json")
        meta_file = os.path.join(session_dir, f"{username}_session.meta.json")
        now = datetime.now()
        meta = {
            "created": None,
            "last_used": None,
            "next_refresh": None
        }
        if os.path.exists(meta_file):
            try:
                with open(meta_file, "r", encoding="utf-8") as mf:
                    meta = json.load(mf)
            except Exception:
                pass

        def parse_time(val):
            if not val:
                return None
            try:
                return datetime.strptime(val, "%Y-%m-%d %H:%M:%S")
            except Exception:
                return None

        created = parse_time(meta.get("created"))
        last_used = parse_time(meta.get("last_used"))
        next_refresh = parse_time(meta.get("next_refresh"))
        session_valid = False
        session_needs_refresh = False
        session_exists = os.path.exists(session_file)
        if session_exists and created:
            age = (now - created).total_seconds() / 3600
            if age >= 6:
                if not next_refresh or next_refresh < now:
                    # 6-15 saat aralığında random bir zaman ata
                    min_dt = created + timedelta(hours=6)
                    max_dt = created + timedelta(hours=15)
                    random_seconds = random.randint(0, int((max_dt - min_dt).total_seconds()))
                    next_refresh = min_dt + timedelta(seconds=random_seconds)
                    meta["next_refresh"] = next_refresh.strftime("%Y-%m-%d %H:%M:%S")
                    # Meta dosyasını hemen güncelle
                    with open(meta_file, "w", encoding="utf-8") as mf:
                        json.dump(meta, mf, indent=4, ensure_ascii=False)
                if next_refresh and now >= next_refresh:
                    session_needs_refresh = True
                else:
                    session_valid = True
            else:
                session_valid = True
        elif session_exists:
            try:
                ctime = datetime.fromtimestamp(os.path.getctime(session_file))
                created = ctime
                meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                meta["next_refresh"] = ""
                with open(meta_file, "w", encoding="utf-8") as mf:
                    json.dump(meta, mf, indent=4, ensure_ascii=False)
                session_valid = True
            except Exception:
                session_valid = False
        try:
            if session_exists and session_valid and not session_needs_refresh:
                try:
                    cl.load_settings(session_file)
                    try:
                        logging.info(f"Oturum test ediliyor (upload - {username}) [get_timeline_feed]...")
                        cl.get_timeline_feed()
                        logging.info(f"Oturum testi başarılı (upload - {username}). Session kullanılacak.")
                    except Exception as e_test:
                        logging.warning(
                            f"Oturum testi başarısız (upload - {username}): {e_test}. Yeniden giriş yapılacak ve oturum güncellenecek.")
                        cl.login(username, password)
                        cl.dump_settings(session_file)
                        created = now
                        meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                        meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                        meta["next_refresh"] = ""
                        with open(meta_file, "w", encoding="utf-8") as mf:
                            json.dump(meta, mf, indent=4, ensure_ascii=False)
                    meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                    with open(meta_file, "w", encoding="utf-8") as mf:
                        json.dump(meta, mf, indent=4, ensure_ascii=False)
                except Exception:
                    cl.login(username, password)
                    cl.dump_settings(session_file)
                    created = now
                    meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                    meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                    meta["next_refresh"] = ""
                    with open(meta_file, "w", encoding="utf-8") as mf:
                        json.dump(meta, mf, indent=4, ensure_ascii=False)
            else:
                cl.login(username, password)
                cl.dump_settings(session_file)
                created = now
                meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                meta["next_refresh"] = ""
                with open(meta_file, "w", encoding="utf-8") as mf:
                    json.dump(meta, mf, indent=4, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Instagram oturum yönetimi hatası: {e}")
            return False
        media_type = ""
        if video_path.lower().endswith((".jpg", ".jpeg", ".png")):
            media = cl.photo_upload(path=video_path, caption=caption)
            media_type = "photo"
        else:
            media = cl.video_upload(path=video_path, caption=caption,
                                    thumbnail=thumbnail_path if os.path.exists(thumbnail_path) else None)
            media_type = "video"
        logging.info(f"Instagram {media_type} başarıyla yüklendi. Media ID: {media.pk}")
        original_url_to_remove = first_item.get("url")
        if first_item in profile_data["downloaded"]:
            profile_data["downloaded"].remove(first_item)
        else:
            profile_data["downloaded"] = [d for d in profile_data["downloaded"] if
                                          d.get("url") != original_url_to_remove]
        if "links" in profile_data and original_url_to_remove:
            profile_data["links"] = [
                link for link in profile_data["links"]
                if (link if isinstance(link, str) else link.get("url", "")) != original_url_to_remove
            ]
        with open(profile_path, "w", encoding="utf-8") as f:
            json.dump(profile_data, f, indent=4, ensure_ascii=False)
        logging.info(f"Profil JSON güncellendi (paylaşım sonrası): {profile_path}")
        files_to_delete = [video_path]
        if os.path.exists(thumbnail_path):
            files_to_delete.append(thumbnail_path)
        for f_to_delete in files_to_delete:
            try:
                if os.path.exists(f_to_delete):
                    os.remove(f_to_delete)
                    logging.info(f"Dosya silindi (paylaşım sonrası): {f_to_delete}")
            except Exception as e:
                logging.error(f"Dosya silinemedi (paylaşım sonrası): {f_to_delete} - {str(e)}")
        return True
    except Exception as e:
        logging.error(f"Instagram video yükleme hatası: {str(e)}")
        return False


def upload_twitter_video_for_profile(profile_data, twitter_output_dir):
    driver = None
    try:
        username = profile_data.get("username")
        password = profile_data.get("password")

        if not username or not password:
            logging.error("Twitter profilinde kullanıcı adı veya şifre eksik.")
            return False

        downloaded = profile_data.get("downloaded", [])
        downloaded = [item for item in downloaded if os.path.exists(item.get("file_path", ""))]
        profile_data["downloaded"] = downloaded

        profile_path = os.path.join("C:\\Users\\<USER>\\PycharmProjects\\Sorcerio", "configuration", "twitter",
                                    f"{username}.json")
        with open(profile_path, "w", encoding="utf-8") as f:
            json.dump(profile_data, f, indent=4, ensure_ascii=False)

        if not downloaded:
            logging.error("downloaded listesi boş. Yüklenecek video yok.")
            return False

        first_item = downloaded[0]
        video_path = os.path.normpath(first_item.get("file_path"))
        final_text = first_item.get("original_caption") or first_item.get("caption", "")

        if not os.path.exists(video_path):
            logging.error(f"Video dosyası bulunamadı: {video_path}")
            profile_data["downloaded"].remove(first_item)
            with open(profile_path, "w", encoding="utf-8") as f:
                json.dump(profile_data, f, indent=4, ensure_ascii=False)
            return False

        driver = create_new_driver_for_account(username)
        if not login_twitter_account(driver, username, password):
            if driver:
                driver.quit()
            return False

        try:
            logging.info(f"{os.path.basename(video_path)} dosyası yükleniyor...")
            driver.get("https://x.com/compose/post")
            time.sleep(8)

            file_input = WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
            )
            file_input.send_keys(video_path)
            logging.info("Video yüklemesi başladı, 40 saniye bekleniyor...")
            time.sleep(40)

            tweet_box = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "div[data-testid='tweetTextarea_0']"))
            )
            driver.execute_script("arguments[0].click();", tweet_box)
            time.sleep(1)
            tweet_box.clear()
            time.sleep(1)
            tweet_box.send_keys(final_text)

            tweet_button_js = """
            var btn = document.querySelector('button[data-testid="tweetButton"], div[data-testid="tweetButton"]');
            if(btn){
                btn.click();
                return true;
            } else {
                return false;
            }
            """
            result = driver.execute_script(tweet_button_js)

            if not result:
                raise Exception("Tweet butonu bulunamadı!")
            logging.info("Tweet gönderildi!")
            time.sleep(25)  # Tweetin işlenmesi için bekleme süresi

            # Paylaşım başarılı, şimdi temizlik yapalım
            original_url_to_remove = first_item.get("url")

            # 1. Fiziksel dosyayı sil (artık 'tweetlenen_folder'a taşımıyoruz)
            try:
                if os.path.exists(video_path):
                    os.remove(video_path)
                    logging.info(f"Paylaşılan Twitter medyası silindi: {video_path}")
            except Exception as e:
                logging.error(f"Paylaşım sonrası Twitter medyası silinemedi: {video_path} - {str(e)}")

            # 2. 'downloaded' listesinden kaldır
            if first_item in profile_data["downloaded"]:
                profile_data["downloaded"].remove(first_item)
            else:  # URL ile eşleşeni bul ve kaldır
                profile_data["downloaded"] = [d for d in profile_data["downloaded"] if
                                              d.get("url") != original_url_to_remove]

            # 3. 'links' listesinden kaldır (eğer varsa)
            if "links" in profile_data and original_url_to_remove:
                profile_data["links"] = [
                    link for link in profile_data["links"]
                    if (link if isinstance(link, str) else link.get("url", "")) != original_url_to_remove
                ]

            # 4. JSON dosyasını güncelle
            with open(profile_path, "w", encoding="utf-8") as f:
                json.dump(profile_data, f, indent=4, ensure_ascii=False)
            logging.info(f"Profil JSON güncellendi (Twitter paylaşımı sonrası): {profile_path}")

            # Arayüz güncellemesi için MainWindow'daki update_stats periyodik olarak çalışacaktır.
            # Anlık güncelleme için, MainWindow'a bir sinyal gönderilebilir veya doğrudan bir metod çağrılabilir.

            return True
        except Exception as e:
            logging.error(f"Twitter video yükleme veya tweet atma hatası: {str(e)}")
            return False
        finally:
            if driver:
                driver.quit()
    except Exception as e:
        logging.error(f"upload_twitter_video_for_profile fonksiyonunda genel hata: {str(e)}")
        return False


def process_youtube_downloads_from_profiles(youtube_output_dir, ffmpeg_dir):
    """
    YouTube için 'configuration/youtube' klasöründeki tüm profil .json dosyalarını okur.
    Her profilin "links" listesindeki her bir linki indirir.
    """
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    config_dir = os.path.join(base_dir, "configuration", "youtube")

    if not os.path.exists(config_dir):
        logging.info(f"YouTube konfigürasyon klasörü bulunamadı: {config_dir}")
        return

    profile_files = [f for f in os.listdir(config_dir) if f.endswith(".json")]
    total_profiles = len(profile_files)
    logging.info(f"Toplam {total_profiles} YouTube profili bulundu.")

    for pfile in profile_files:
        profile_path = os.path.join(config_dir, pfile)
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            links = data.get("links", [])
            downloaded_urls = {item["url"] for item in data.get("downloaded", []) if
                               isinstance(item, dict) and "url" in item}
            links = [link for link in links if link not in downloaded_urls]
            if not links:
                logging.info(f"Bu profilde indirilecek yeni link yok: {pfile}")
                continue
            logging.info(f"--- Profil: {pfile}, Link sayısı: {len(links)} ---")
            for i, url in enumerate(links, 1):
                logging.info(f"İndiriliyor (YouTube): {url} ({i}/{len(links)})")
                success = download_youtube_video(url, youtube_output_dir, ffmpeg_dir)
                if success:
                    video_type = "Shorts" if is_shorts_url(url) else "Video"
                    logging.info(f"YouTube {video_type} indirildi ({i}/{len(links)})")
                else:
                    logging.error(f"YouTube video indirilemedi ({i}/{len(links)})")
                time.sleep(1)
        except Exception as e:
            logging.error(f"Profil dosyası okunurken hata oluştu: {pfile} -> {str(e)}")


def process_twitter_downloads_from_profiles(twitter_output_dir, ffmpeg_dir):
    """
    Twitter için 'configuration/twitter' klasöründeki tüm profil .json dosyalarını okur.
    Her profilin "links" listesindeki her bir linki (Twitter veya Instagram) indirir.
    """
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    config_dir = os.path.join(base_dir, "configuration", "twitter")
    videos_dir = os.path.join(base_dir, "videos")
    # Geçici dosyalar için kullanılacak dizin (varsayılan temp_dir veya benzeri)
    temp_dir = os.path.join(videos_dir, "temp")

    # Gerekli dizinlerin var olduğundan emin ol
    if not os.path.exists(config_dir):
        logging.info(f"Twitter konfigürasyon klasörü bulunamadı: {config_dir}")
        return

    create_directory(videos_dir)  # videos dizinini oluştur (eğer yoksa)
    create_directory(temp_dir)  # temp dizinini oluştur (eğer yoksa)

    profile_files = [f for f in os.listdir(config_dir) if f.endswith(".json")]
    total_profiles = len(profile_files)
    logging.info(f"Toplam {total_profiles} Twitter profili bulundu.")

    for pfile in profile_files:
        profile_path = os.path.join(config_dir, pfile)
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # İndirilenler listesini güvenli bir şekilde al ve sadece sözlük olanları filtrele
            downloaded = [item for item in data.get("downloaded", []) if isinstance(item, dict)]

            # İndirilmiş URL'lerin setini oluştur
            downloaded_urls = {item.get("url") for item in downloaded if item.get("url")}

            # Henüz indirilmemiş linkleri belirle
            links_to_process = [link for link in data.get("links", []) if link not in downloaded_urls]

            if not links_to_process:
                logging.info(f"Bu profilde indirilecek yeni link yok: {pfile}")
                continue

            logging.info(f"--- Profil: {pfile}, İndirilecek Link sayısı: {len(links_to_process)} ---")

            # İndirilecek her linki işle
            for i, url in enumerate(links_to_process, 1):
                logging.info(f"İndiriliyor (Twitter Profili İçinde): {url} ({i}/{len(links_to_process)})")

                success_download = False
                caption = None
                file_path = None
                original_caption = None  # Instagram için orijinal başlığı tutar

                if "instagram.com" in url:
                    # Instagram linkini indirmek için global download_instagram_post fonksiyonunu kullan
                    # download_instagram_post fonksiyonunun (success, caption, file_path, original_caption) döndürdüğünü varsayıyoruz
                    success_download, caption, file_path, original_caption = download_instagram_post(url,
                                                                                                     twitter_output_dir)
                elif "twitter.com" in url or "x.com" in url:
                    # Twitter linkini indirmek için güncellenmiş download_twitter_media fonksiyonunu kullan
                    # download_twitter_media fonksiyonunun (success, caption, file_path) döndürdüğünü varsayıyoruz
                    success_download, caption, file_path = download_twitter_media(url, twitter_output_dir, ffmpeg_dir)
                    # Twitter indirmeleri için original_caption None kalacaktır.

                # İndirme başarılı olduysa ve dosya yolu ile başlık varsa
                if success_download and file_path and caption:
                    logging.info(f"Medya başarıyla indirildi: {file_path}")
                    # İndirilen öğeyi downloaded listesine ekle
                    downloaded.append({
                        "url": url,
                        "file_path": file_path,
                        "caption": caption,  # Kullanılacak güvenli başlık
                        "original_caption": original_caption  # Instagram için varsa orijinal başlık
                    })

                    # Güncellenmiş downloaded listesini data içine kaydet
                    data["downloaded"] = downloaded

                    # Güncellenmiş profil verisini JSON dosyasına geri yaz
                    with open(profile_path, "w", encoding="utf-8") as f:
                        json.dump(data, f, indent=4, ensure_ascii=False)

                else:
                    logging.error(f"Medya indirilemedi ({i}/{len(links_to_process)}): {url}")

                # İstekler arasında bekleme ekle (oran sınırlamalarını aşmamak için)
                time.sleep(random.uniform(5, 10))  # Örnek bekleme süresi


        except Exception as e:
            logging.error(f"Profil dosyası işlenirken hata oluştu: {pfile} -> {str(e)}")


# ---------- Instagram Video Yükleme (Rastgele Seçim) ----------
def upload_random_instagram_video(instagram_output_dir):
    """
    Instagram video paylaşım fonksiyonu:
    - 'instagramdownloaded' klasöründen rastgele bir video seçer
    - Dosya adını (uzantısız) açıklama olarak kullanır
    - Videoyu Instagram'a yükler
    """
    try:
        username = "craftersgame"  # Örnek: konfigürasyondan ya da sabit
        password = "1234abc..."
        videos = [
            os.path.join(instagram_output_dir, f)
            for f in os.listdir(instagram_output_dir)
            if f.lower().endswith((".mp4", ".mov", ".mkv", ".webm"))
        ]
        if not videos:
            logging.error("instagramdownloaded klasöründe yüklenecek video bulunamadı.")
            return
        selected_video = random.choice(videos)
        logging.info(f"Seçilen video: {selected_video}")
        video_caption = os.path.splitext(os.path.basename(selected_video))[0]
        logging.info(f"Açıklama olarak kullanılacak: {video_caption}")
        duration = dummy_duration(selected_video)
        if duration < 1:
            logging.error("Video çok kısa (1 saniyeden az).")
            return
        if duration > 60:
            logging.warning("Video 60 saniyeden uzun; Instagram tarafından reddedilebilir.")
        cl = Client()
        cl.login(username, password)
        try:
            media = cl.video_upload(path=selected_video, caption=video_caption)
            logging.info(f"Video başarıyla Instagram'a yüklendi. Media ID: {media.pk}")
            return True
        except Exception as e:
            logging.error(f"Video yüklenirken hata oluştu: {str(e)}")
            return False
    except Exception as e:
        logging.error(f"Instagram video yükleme hatası: {str(e)}")
        return False


def should_process_profile(profile_data):
    """Profilin şu anki saatte işlenip işlenmeyeceğini kontrol eder."""
    now = datetime.now()
    current_day = now.strftime("%A").lower()  # "monday", "tuesday" vs.
    scheduled_times = profile_data.get("schedule", {}).get(current_day, [])

    for time_str in scheduled_times:
        try:
            scheduled_time = datetime.strptime(time_str, "%H:%M").time()
            if (now.hour == scheduled_time.hour) and (now.minute == scheduled_time.minute):
                return True
        except ValueError:
            continue
    return False


# <<< YENİ EKLENEN >>>
def process_profiles_with_schedule():
    """
    Her dakika çalışıp, profil JSON'larındaki schedule saatlerine bakarak
    işlenecek profilleri tespit eder ve onların linklerini işler.
    """
    # Aşağıdaki BASE_DIR, sizin projenizde 'base_dir' olarak da geçiyor olabilir
    BASE_DIR = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    platforms = ["instagram", "twitter"]

    # Örnek olarak ffmpeg yolunu vs. buradan da alabiliriz
    videos_dir = os.path.join(BASE_DIR, "videos")
    temp_dir = os.path.join(videos_dir, "temp")
    ffmpeg_dir = download_ffmpeg()

    # İndirme çıktıları
    instagram_output_dir = os.path.join(videos_dir, "instagramdownloaded")
    twitter_output_dir = os.path.join(videos_dir, "twitterdownloaded")

    for platform in platforms:
        config_dir = os.path.join(BASE_DIR, "configuration", platform)
        if not os.path.exists(config_dir):
            continue

        for profile_file in os.listdir(config_dir):
            if not profile_file.endswith(".json"):
                continue
            profile_path = os.path.join(config_dir, profile_file)
            with open(profile_path, "r", encoding="utf-8") as f:
                profile_data = json.load(f)

            # Zaman kontrolü
            if should_process_profile(profile_data):
                # Bu profili işleyelim
                if platform == "instagram":
                    # Instagram profili içindeki hem instagram hem twitter linklerini işle
                    process_profile_links("instagram", instagram_output_dir, temp_dir, ffmpeg_dir)
                    process_profile_links("twitter", instagram_output_dir, temp_dir, ffmpeg_dir)
                elif platform == "twitter":
                    # Twitter profili içindeki hem twitter hem instagram linklerini işle
                    process_profile_links("twitter", twitter_output_dir, temp_dir, ffmpeg_dir)
                    process_profile_links("instagram", twitter_output_dir, temp_dir, ffmpeg_dir)


def start_scheduler():
    """
    Arka planda her 1 dakikada bir 'process_profiles_with_schedule' fonksiyonunu tetikler.
    """
    scheduler = BackgroundScheduler()
    scheduler.add_job(process_profiles_with_schedule, 'interval', minutes=1)
    scheduler.start()


# ---------- Configuration Dosyalarının Oluşturulması ----------
def setup_configurations():
    """
    Ana dizinde configuration adlı klasör oluşturur.
    Bu klasör altında twitter ve instagram adında iki alt klasör oluşturulur.
    Her biri için 8 adet profile json dosyası oluşturulur.
    Her json dosyasında:
      - username
      - password
      - schedule (gün bazlı -> liste, her güne istediğiniz sayıda saat)
      - hashtags (liste)
      - links (tek bir liste, gün bazlı değil: eklenecek video linkleri)
    Ayrıca, Instagram için oturum dosyalarının bulunduğu `sessions` klasöründe
    JSON içinde artık tanımlı olmayan kullanıcı adlarına ait session dosyaları silinir.
    """
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    config_dir = os.path.join(base_dir, "configuration")
    twitter_config_dir = os.path.join(config_dir, "twitter")
    instagram_config_dir = os.path.join(config_dir, "instagram")

    os.makedirs(twitter_config_dir, exist_ok=True)
    os.makedirs(instagram_config_dir, exist_ok=True)

    default_schedule = {
        "monday": [],
        "tuesday": [],
        "wednesday": [],
        "thursday": [],
        "friday": [],
        "saturday": [],
        "sunday": [],
    }

    default_links = []

    default_config = {
        "username": "",
        "password": "",
        "schedule": default_schedule,
        "hashtags": [],
        "links": default_links,
        "downloaded": [],
    }

    # Twitter profilleri
    for i in range(1, 9):
        config_file = os.path.join(twitter_config_dir, f"profile{i}.json")
        if not os.path.exists(config_file):
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(default_config, f, indent=4)
        else:
            try:
                with open(config_file, "r", encoding="utf-8") as f:
                    existing = json.load(f)
                if (not existing.get("username")
                        and not existing.get("password")
                        and not existing.get("links")):
                    logging.info(f"{config_file} boş olduğu için profil yeniden oluşturulmadı.")
            except Exception as e:
                logging.warning(f"{config_file} kontrol edilirken hata: {e}")

    # Instagram profilleri
    for i in range(1, 9):
        config_file = os.path.join(instagram_config_dir, f"profile{i}.json")
        if not os.path.exists(config_file):
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(default_config, f, indent=4)
        else:
            try:
                with open(config_file, "r", encoding="utf-8") as f:
                    existing = json.load(f)
                if (not existing.get("username")
                        and not existing.get("password")
                        and not existing.get("links")):
                    logging.info(f"{config_file} boş olduğu için profil yeniden oluşturulmadı.")
            except Exception as e:
                logging.warning(f"{config_file} kontrol edilirken hata: {e}")

    logging.info("Configuration dosyaları oluşturuldu veya mevcut.")

    # --- Silinmiş profillere ait Instagram session dosyalarını temizle ---
    session_dir = os.path.join(instagram_config_dir, "sessions")
    if os.path.isdir(session_dir):
        # 1) JSON içindeki aktif kullanıcı adlarını topla
        mevcut_userlar = set()
        for profile_file in os.listdir(instagram_config_dir):
            if not profile_file.endswith(".json"):
                continue
            profile_path = os.path.join(instagram_config_dir, profile_file)
            try:
                with open(profile_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                username = data.get("username", "").strip()
                if username:
                    mevcut_userlar.add(username)
            except Exception as e:
                logging.warning(f"Profil JSON okunurken hata ({profile_file}): {e}")

        # 2) Session klasöründeki her "<username>_session.json" için doğrula
        for sf in os.listdir(session_dir):
            if sf.endswith("_session.json"):
                uname = sf[:-len("_session.json")]
                if uname not in mevcut_userlar:
                    try:
                        os.remove(os.path.join(session_dir, sf))
                        logging.info(f"Silinen profil oturumu silindi: {sf}")
                    except Exception as e:
                        logging.error(f"Session dosyası silinirken hata ({sf}): {e}")

    return config_dir


def initialize_sessions_on_startup():
    """
    Program başlangıcında Instagram session dosyalarını SİLMEZ. Her profil için session ve meta dosyası kontrol edilir.
    Eğer session geçerliyse kullanılmaya devam edilir, geçerli değilse yeni giriş yapılır ve session güncellenir.
    """
    logging.info("Tüm Instagram session'ları başlangıçta kontrol ediliyor...")
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    instagram_config_dir = os.path.join(base_dir, "configuration", "instagram")
    session_dir = os.path.join(instagram_config_dir, "sessions")
    os.makedirs(session_dir, exist_ok=True)

    profile_files = [f for f in os.listdir(instagram_config_dir) if f.endswith(".json")]
    logging.info(f"Yeniden giriş için {len(profile_files)} Instagram profili bulundu.")

    for profile_file_name in profile_files:
        profile_path = os.path.join(instagram_config_dir, profile_file_name)
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                profile_data = json.load(f)

            username = profile_data.get("username", "").strip()
            password = profile_data.get("password", "").strip()

            if not username or not password:
                logging.warning(f"Profil {profile_file_name} için kullanıcı adı veya şifre eksik, atlanıyor.")
                continue

            session_file_path = os.path.join(session_dir, f"{username}_session.json")
            meta_file = os.path.join(session_dir, f"{username}_session.meta.json")
            now = datetime.now()
            meta = {
                "created": None,
                "last_used": None,
                "next_refresh": None
            }
            if os.path.exists(meta_file):
                try:
                    with open(meta_file, "r", encoding="utf-8") as mf:
                        meta = json.load(mf)
                except Exception:
                    pass

            def parse_time(val):
                if not val:
                    return None
                try:
                    return datetime.strptime(val, "%Y-%m-%d %H:%M:%S")
                except Exception:
                    return None

            created = parse_time(meta.get("created"))
            last_used = parse_time(meta.get("last_used"))
            next_refresh = parse_time(meta.get("next_refresh"))
            session_valid = False
            session_needs_refresh = False
            session_exists = os.path.exists(session_file_path)
            if session_exists and created:
                age = (now - created).total_seconds() / 3600
                if age >= 6:
                    if not next_refresh or next_refresh < now:
                        # 6-15 saat aralığında random bir zaman ata
                        min_dt = created + timedelta(hours=6)
                        max_dt = created + timedelta(hours=15)
                        random_seconds = random.randint(0, int((max_dt - min_dt).total_seconds()))
                        next_refresh = min_dt + timedelta(seconds=random_seconds)
                        meta["next_refresh"] = next_refresh.strftime("%Y-%m-%d %H:%M:%S")
                        # Meta dosyasını hemen güncelle
                        with open(meta_file, "w", encoding="utf-8") as mf:
                            json.dump(meta, mf, indent=4, ensure_ascii=False)
                    if next_refresh and now >= next_refresh:
                        session_needs_refresh = True
                    else:
                        session_valid = True
                else:
                    session_valid = True
            elif session_exists:
                try:
                    ctime = datetime.fromtimestamp(os.path.getctime(session_file_path))
                    created = ctime
                    meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                    meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                    meta["next_refresh"] = ""
                    with open(meta_file, "w", encoding="utf-8") as mf:
                        json.dump(meta, mf, indent=4, ensure_ascii=False)
                    session_valid = True
                except Exception:
                    session_valid = False
            cl = Client()
            try:
                if session_exists and session_valid and not session_needs_refresh:
                    try:
                        cl.load_settings(session_file_path)
                        try:
                            logging.info(f"Oturum test ediliyor (startup - {username}) [get_timeline_feed]...")
                            cl.get_timeline_feed()
                            logging.info(f"Oturum testi başarılı (startup - {username}). Session kullanılacak.")
                        except Exception as e_test:
                            logging.warning(
                                f"Oturum testi başarısız (startup - {username}): {e_test}. Yeniden giriş yapılacak ve oturum güncellenecek.")
                            cl.login(username, password)
                            cl.dump_settings(session_file_path)
                            created = now
                            meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                            meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                            meta["next_refresh"] = ""
                            with open(meta_file, "w", encoding="utf-8") as mf:
                                json.dump(meta, mf, indent=4, ensure_ascii=False)
                        meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                        with open(meta_file, "w", encoding="utf-8") as mf:
                            json.dump(meta, mf, indent=4, ensure_ascii=False)
                    except Exception:
                        cl.login(username, password)
                        cl.dump_settings(session_file_path)
                        created = now
                        meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                        meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                        meta["next_refresh"] = ""
                        with open(meta_file, "w", encoding="utf-8") as mf:
                            json.dump(meta, mf, indent=4, ensure_ascii=False)
                else:
                    cl.login(username, password)
                    cl.dump_settings(session_file_path)
                    created = now
                    meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                    meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                    meta["next_refresh"] = ""
                    with open(meta_file, "w", encoding="utf-8") as mf:
                        json.dump(meta, mf, indent=4, ensure_ascii=False)
            except Exception as e:
                logging.error(f"Instagram oturum yönetimi hatası (startup): {e}")
        except Exception as e:
            logging.error(f"Profil dosyası ({profile_file_name}) işlenirken hata: {str(e)}")
        time.sleep(random.uniform(2, 5))
    logging.info("Instagram session kontrol işlemi tamamlandı.")


def initialize_twitter_sessions_on_startup():
    """
    Program başlangıcında tüm Twitter session (Selenium profil) klasörlerini temizler,
    konfigüre edilmiş tüm Twitter profillerine yeniden giriş yapmayı dener.
    """
    logging.info("Tüm Twitter session'ları (Selenium profilleri) başlangıçta yenileniyor...")
    base_project_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    twitter_config_dir = os.path.join(base_project_dir, "configuration", "twitter")

    if not os.path.exists(twitter_config_dir):
        logging.warning(f"Twitter konfigürasyon dizini bulunamadı: {twitter_config_dir}")
        return

    profile_files = [f for f in os.listdir(twitter_config_dir) if f.endswith(".json")]
    logging.info(f"Yeniden giriş için {len(profile_files)} Twitter profili bulundu.")

    for profile_file_name in profile_files:
        profile_path = os.path.join(twitter_config_dir, profile_file_name)
        username_from_file = ""
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                profile_data = json.load(f)

            username = profile_data.get("username", "").strip()
            password = profile_data.get("password", "").strip()
            username_from_file = username  # Keep track for folder deletion

            if not username or not password:
                logging.warning(f"Profil {profile_file_name} için kullanıcı adı veya şifre eksik, atlanıyor.")
                continue

            # 1. Eski Selenium profil klasörünü sil
            user_profile_dir_path = os.path.join(base_project_dir, f"chrome_profile_{username}")
            if os.path.exists(user_profile_dir_path):
                MAX_ATTEMPTS = 3
                RETRY_DELAY = 5  # saniye
                attempts = 0
                deleted_successfully = False
                while attempts < MAX_ATTEMPTS:
                    try:
                        # Silmeden önce ilgili driver'ın kapalı olduğundan emin ol
                        try:
                            import psutil
                            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                                    if user_profile_dir_path in ' '.join(proc.info['cmdline']):
                                        proc.terminate()
                                        proc.wait(timeout=5)
                        except Exception as e:
                            logging.warning(f"Profil silme öncesi chrome işlemi kapatılamadı: {e}")

                        shutil.rmtree(user_profile_dir_path)
                        logging.info(f"Eski Twitter profil klasörü silindi: {user_profile_dir_path}")
                        deleted_successfully = True
                        break  # Başarılı oldu, döngüden çık
                    except Exception as e:
                        attempts += 1
                        logging.warning(
                            f"Twitter profil klasörü ({user_profile_dir_path}) silinirken hata (deneme {attempts}/{MAX_ATTEMPTS}): {str(e)}"
                        )
                        if attempts < MAX_ATTEMPTS:
                            logging.info(f"{RETRY_DELAY} saniye sonra tekrar denenecek...")
                            time.sleep(RETRY_DELAY)
                        else:
                            logging.error(
                                f"Twitter profil klasörü ({user_profile_dir_path}) {MAX_ATTEMPTS} denemeden sonra silinemedi. "
                                f"Muhtemelen klasör başka bir işlem tarafından kullanılıyor. "
                                f"Bilgisayarı yeniden başlatıp tekrar deneyin veya ilgili işlemi manuel olarak kapatın."
                            )
                            break  # Silme başarısız olsa da döngüden çık, loglama yapıldı.

            # 2. Yeni driver oluştur ve giriş yap (bu, profil klasörünü yeniden oluşturacak)
            logging.info(f"Twitter'a giriş yapılıyor: {username} ({profile_file_name})")
            driver = None
            try:
                driver = create_new_driver_for_account(username)
                if login_twitter_account(driver, username, password):
                    logging.info(
                        f"Twitter'a giriş başarılı ve session (profil klasörü) oluşturuldu/güncellendi: {username}")
                else:
                    logging.warning(f"Twitter'a giriş başarısız: {username}")
            except Exception as login_exc:
                logging.error(f"Twitter için driver oluşturma veya giriş hatası ({username}): {str(login_exc)}")
            finally:
                if driver:
                    driver.quit()

        except Exception as e:
            logging.error(f"Twitter profil dosyası ({profile_file_name}) işlenirken genel hata: {str(e)}")
            # Eğer kullanıcı adı okunduysa ama genel bir hata oluştuysa yine de profil klasörünü silmeyi dene
            if username_from_file:
                user_profile_dir_path_on_error = os.path.join(base_project_dir, f"chrome_profile_{username_from_file}")
                if os.path.exists(user_profile_dir_path_on_error):
                    try:
                        shutil.rmtree(user_profile_dir_path_on_error)
                        logging.info(
                            f"Hata sonrası eski Twitter profil klasörü silindi: {user_profile_dir_path_on_error}")
                    except Exception as e_rm:
                        logging.error(
                            f"Hata sonrası Twitter profil klasörü ({user_profile_dir_path_on_error}) silinirken hata: {str(e_rm)}")

        time.sleep(random.uniform(3, 7))  # Giriş denemeleri arasında daha uzun bekleme

    logging.info("Twitter session (Selenium profilleri) yenileme işlemi tamamlandı.")


# ---------- Konsol Tabanlı Ana Program (Artık GUI ile entegre) ----------
def console_main():
    logger = setup_logging()
    base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
    videos_dir = os.path.join(base_dir, "videos")
    os.makedirs(videos_dir, exist_ok=True)

    instagram_output_dir = os.path.join(videos_dir, "instagramdownloaded")
    youtube_output_dir = os.path.join(videos_dir, "youtubedownloaded")
    twitter_output_dir = os.path.join(videos_dir, "twitterdownloaded")
    temp_dir = os.path.join(videos_dir, "temp")

    create_directory(instagram_output_dir)
    create_directory(youtube_output_dir)
    create_directory(twitter_output_dir)
    create_directory(temp_dir)

    setup_configurations()

    print("Lütfen yapmak istediğiniz işlemi seçin:")
    print("1 - Video İndirme (Artık .txt yerine profil JSON dosyalarındaki linkler kullanılır)")
    print("2 - Instagram Video Paylaşma (instagramdownloaded klasöründen rastgele video)")
    print("3 - Twitter Video Tweetleme (twitterdownloaded klasöründen rastgele video)")
    choice = input("Seçiminiz (1, 2 veya 3): ").strip()

    if choice == "1":
        try:
            ffmpeg_dir = download_ffmpeg()
            if not ffmpeg_dir:
                logging.error("ffmpeg kurulamadı, YouTube/Twitter videoları indirilemeyecek")
                return
            # JSON dosyalarından okunan linkler üzerinden indirme işlemleri
            process_instagram_downloads_from_profiles(instagram_output_dir, temp_dir)
            process_youtube_downloads_from_profiles(youtube_output_dir, ffmpeg_dir)
            process_twitter_downloads_from_profiles(twitter_output_dir, ffmpeg_dir)
        except Exception as e:
            logging.error(f"Ana program hatası: {str(e)}")
        finally:
            clean_temp_files(temp_dir)
    elif choice == "2":
        upload_random_instagram_video(instagram_output_dir)
    elif choice == "3":
        upload_random_twitter_video(twitter_output_dir)
    else:
        print("Geçersiz seçim. Lütfen 1, 2 veya 3 girin.")


class JsBridge(QObject):
    def __init__(self, mainWindow):
        super().__init__()
        self.mainWindow = mainWindow
        self.dragPos = None
        self.windowPos = None
        self.destekDialog = None  # referans baştan ekle

    @pyqtSlot()
    def minimizeWindow(self):
        self.mainWindow.showMinimized()

    @pyqtSlot()
    def maximizeWindow(self):
        view = self.mainWindow.view
        # Eğer zaten max durumdaysa: normal moda dön ve kaydedilmiş deck boyutlarını geri yükle
        if self.mainWindow.isMaximized():
            self.mainWindow.showNormal()
            view.page().runJavaScript("""
                if (window._manualDeckSizes) {
                    const [l, m, r, e] = window._manualDeckSizes;
                    document.querySelector('.grid-container')
                        .style.gridTemplateColumns = `${l}px 4px ${m}px 4px ${r}px 4px ${e}px`;
                }
            """)
        else:
            # Max moda geçmeden önce şimdiki deck genişliklerini kaydet
            view.page().runJavaScript("""
                // Eğer mouse ile ayarlanmışsa, onları kaydet
                if (!window._manualDeckSizes) {
                    window._manualDeckSizes = [
                        document.querySelector('.left-deck').offsetWidth,
                        document.querySelector('.middle-deck').offsetWidth,
                        document.querySelector('.right-deck').offsetWidth,
                        document.querySelector('.extra-deck').offsetWidth
                    ];
                } else {
                    // Son mouse ile ayarlanan değerleri güncelle
                    window._manualDeckSizes = [
                        document.querySelector('.left-deck').offsetWidth,
                        document.querySelector('.middle-deck').offsetWidth,
                        document.querySelector('.right-deck').offsetWidth,
                        document.querySelector('.extra-deck').offsetWidth
                    ];
                }
                // Tam ekran için orantılı (fr) layout'a geri dön
                document.querySelector('.grid-container')
                    .style.gridTemplateColumns = '0.64fr 4px 1fr 4px 1fr 4px 1fr';
            """)
            self.mainWindow.showMaximized()

        # Mouse ile boyut değiştirildiğinde _manualDeckSizes güncellensin
        view.page().runJavaScript("""
            function updateManualDeckSizes() {
                window._manualDeckSizes = [
                    document.querySelector('.left-deck').offsetWidth,
                    document.querySelector('.middle-deck').offsetWidth,
                    document.querySelector('.right-deck').offsetWidth,
                    document.querySelector('.extra-deck').offsetWidth
                ];
            }
            // Resizer event'lerine ekle
            ['resizer-1','resizer-2','resizer-3'].forEach(function(id) {
                var resizer = document.getElementById(id);
                if (resizer && !resizer._deckSizeListener) {
                    resizer.addEventListener('mouseup', updateManualDeckSizes);
                    resizer._deckSizeListener = true;
                }
            });
        """)

    @pyqtSlot()
    def closeWindow(self):
        self.mainWindow.close()

    @pyqtSlot()
    def startDrag(self):
        self.dragPos = QCursor.pos()
        self.windowPos = self.mainWindow.pos()

    @pyqtSlot()
    def dragWindow(self):
        if self.dragPos is not None and self.windowPos is not None:
            delta = QCursor.pos() - self.dragPos
            self.mainWindow.move(self.windowPos + delta)

    @pyqtSlot()
    def stopDrag(self):
        self.dragPos = None
        self.windowPos = None

    @pyqtSlot()
    def openDestekWindow(self):
        if getattr(self, 'destekDialog', None) is not None:
            self.destekDialog.close()
            self.destekDialog = None
        self.destekDialog = DestekDialog(self.mainWindow)
        self.destekDialog.destroyed.connect(lambda: setattr(self, 'destekDialog', None))
        self.destekDialog.show()

    @pyqtSlot()
    def startProcessingFromJS(self):
        self.mainWindow.startProcessing()

    @pyqtSlot()
    def stopProcessing(self):
        self.mainWindow.stopProcessing()


class ProfileEditorDialog(QDialog):
    """
    Profil düzenleme penceresi (Pop-up).
    Saat ayarları için "Saat Ayarları" butonu,
    Link Ekle için "Link Ekle" butonu,
    Kaydet/kapat için alt bar vs.
    """
    profileNameChanged = pyqtSignal(str, str)  # (profilePath, newProfileName)

    def __init__(self, profilePath, parent=None):
        super().__init__(parent)
        self.profilePath = profilePath
        try:
            with open(self.profilePath, "r", encoding="utf-8") as f:
                self.data = json.load(f)
        except Exception as e:
            self.data = {}
            logging.error(f"JSON okunurken hata: {e}")

        # Kullanıcı adı yoksa, dosya adı ile gösterelim
        self.profileName = self.data.get("username", "").strip()

        # schedule verisi (gün bazlı saatler)
        if "schedule" not in self.data:
            self.data["schedule"] = {
                "monday": [],
                "tuesday": [],
                "wednesday": [],
                "thursday": [],
                "friday": [],
                "saturday": [],
                "sunday": [],
            }

        # links verisi (tek liste)
        if "links" not in self.data:
            self.data["links"] = []

        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setWindowModality(Qt.ApplicationModal)
        self.setModal(True)
        self.resize(314, 360)
        self.centerDialog()

        shadow = QGraphicsDropShadowEffect(self)
        shadow.setOffset(0, 0)
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 180))
        self.setGraphicsEffect(shadow)

        self.mainLayout = QVBoxLayout(self)
        self.mainLayout.setContentsMargins(0, 0, 0, 0)
        self.mainLayout.setSpacing(0)

        # ------------------ Başlık Çubuğu ------------------
        self.titleBar = QFrame()
        self.titleBar.setObjectName("titleBar")
        self.titleBar.setFixedHeight(32)
        self.titleBarLayout = QHBoxLayout(self.titleBar)
        self.titleBarLayout.setContentsMargins(8, 0, 8, 0)
        self.titleBarLayout.setSpacing(0)

        self.emptyLabel = QLabel("")
        self.titleBarLayout.addWidget(self.emptyLabel, 1)

        self.closeBtn = QPushButton("?")
        self.closeBtn.setFixedSize(24, 24)
        self.closeBtn.setStyleSheet(
            """
            QPushButton {
                background-color: #2B2B2B;
                border: none;
                border-radius: 4px;
                color: #FFFFFF;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
            """
        )
        self.closeBtn.clicked.connect(self.close)
        self.titleBarLayout.addWidget(self.closeBtn, 0, alignment=Qt.AlignRight)

        # ------------------ İçerik Bölgesi ------------------
        self.contentWidget = QWidget()
        self.contentLayout = QVBoxLayout(self.contentWidget)
        self.contentLayout.setContentsMargins(20, 6, 20, 16)
        self.contentLayout.setSpacing(8)
        self.contentLayout.setAlignment(Qt.AlignCenter)

        # ------------------ Profil Adı Konteyneri ve Etiketi ------------------
        # Dinamik olarak metne göre boyutlanan etiket,
        # fakat konteyner sabit yüksekliğe sahip; böylece diğer öğeler yer değiştirmez.
        self.profileNameLabel = QLabel(self.profileName if self.profileName else "")
        fontProfileName = QFont("Segoe UI", 13, QFont.Bold)
        self.profileNameLabel.setFont(fontProfileName)
        self.profileNameLabel.setAlignment(Qt.AlignCenter)
        if self.profileName:
            self.profileNameLabel.setStyleSheet("""
                color: black;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #ffe082,
                    stop: 0.5 #fff9c4,
                    stop: 1 #ffd54f
                );
                border: 1px solid #d6a300;
                border-radius: 6px;
                padding: 6px 12px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 16px;
                font-weight: bold;
                letter-spacing: 0.2px;
            """)
        else:
            self.profileNameLabel.setStyleSheet("""
                background: transparent;
                border: none;
            """)

        # Konteyner widget'ı oluşturuyoruz
        self.profileNameContainer = QWidget()
        # Örneğin sabit 40 piksel yüksekliğinde (ihtiyaca göre ayarlayın)
        self.profileNameContainer.setFixedHeight(40)
        containerLayout = QHBoxLayout(self.profileNameContainer)
        containerLayout.setContentsMargins(0, 0, 0, 0)
        containerLayout.setAlignment(Qt.AlignCenter)
        containerLayout.addWidget(self.profileNameLabel)
        self.contentLayout.addWidget(self.profileNameContainer, 0, Qt.AlignCenter)
        self.contentLayout.addSpacing(2)

        self.infoLabel = QLabel("")
        self.infoLabel.setFixedHeight(18)
        self.infoLabel.setStyleSheet("color: #B3B3B3; font-size: 10px; margin-bottom: 2px;")
        self.infoLabel.setAlignment(Qt.AlignCenter)
        self.contentLayout.addWidget(self.infoLabel, 0, Qt.AlignCenter)

        # ------------------ Form Bölümü ------------------
        self.formLayoutContainer = QWidget()
        self.formLayoutContainerLayout = QVBoxLayout(self.formLayoutContainer)
        self.formLayoutContainerLayout.setContentsMargins(0, 0, 0, 0)
        self.formLayoutContainerLayout.setSpacing(5)
        self.formLayoutContainerLayout.setAlignment(Qt.AlignCenter)

        self.formLayout = QFormLayout()
        self.formLayout.setVerticalSpacing(8)
        self.formLayout.setHorizontalSpacing(15)
        self.formLayout.setAlignment(Qt.AlignCenter)
        self.formLayout.setFormAlignment(Qt.AlignCenter)
        self.formLayout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # Kullanıcı Adı
        self.usernameLabel = QLabel("Kullanıcı Adı:")
        self.usernameLabel.setStyleSheet("font: bold 12px 'Segoe UI'; color: white;")
        self.usernameEdit = QLineEdit()
        self.usernameEdit.setStyleSheet(
            """
            font: 11px 'Segoe UI';
            color: white;
            background-color: #1E1E1E;
            border: 1px solid #373737;
            border-radius: 4px;
            padding: 5px;
        """
        )
        self.usernameEdit.setText(self.data.get("username", ""))
        self.usernameEdit.setFixedWidth(180)
        self.formLayout.addRow(self.usernameLabel, self.usernameEdit)
        self.usernameEdit.textChanged.connect(self.onUsernameChanged)

        # Şifre
        self.passwordLabel = QLabel("Şifre:")
        self.passwordLabel.setStyleSheet("font: bold 12px 'Segoe UI'; color: white;")
        self.passwordContainer = QFrame()
        self.passwordContainer.setStyleSheet(
            """
            background-color: #1E1E1E;
            border: 1px solid #373737;
            border-radius: 4px;
        """
        )
        self.passwordContainer.setFixedWidth(180)
        pwContainerLayout = QHBoxLayout(self.passwordContainer)
        pwContainerLayout.setContentsMargins(0, 0, 0, 0)
        pwContainerLayout.setSpacing(0)
        self.passwordEdit = QLineEdit()
        self.passwordEdit.setStyleSheet(
            """
            font: 11px 'Segoe UI';
            color: white;
            background-color: transparent;
            border: none;
            padding: 5px;
        """
        )
        self.passwordEdit.setEchoMode(QLineEdit.Normal)
        self.passwordEdit.setText(self.data.get("password", ""))
        pwContainerLayout.addWidget(self.passwordEdit)
        self.formLayout.addRow(self.passwordLabel, self.passwordContainer)

        # Hashtags
        self.hashtagsLabel = QLabel("Hashtags:")
        self.hashtagsLabel.setStyleSheet("font: bold 12px 'Segoe UI'; color: white;")
        self.hashtagsEdit = QLineEdit()
        self.hashtagsEdit.setStyleSheet(
            """
            font: 11px 'Segoe UI';
            color: white;
            background-color: #1E1E1E;
            border: 1px solid #373737;
            border-radius: 4px;
            padding: 5px;
        """
        )
        self.hashtagsEdit.setFixedWidth(180)
        self.hashtagsEdit.setPlaceholderText("Örnek: #funny, #cat")
        hashtags_list = self.data.get("hashtags", [])
        self.hashtagsEdit.setText(",".join(hashtags_list))
        self.formLayout.addRow(self.hashtagsLabel, self.hashtagsEdit)

        self.formLayoutContainerLayout.addLayout(self.formLayout)
        self.formLayoutContainerLayout.addSpacing(15)

        # Saat Ayarları Butonu
        self.timeSettingsBtn = QPushButton("Saat Ayarları")
        self.timeSettingsBtn.setStyleSheet(
            """
            QPushButton {
                background-color: #242424;
                border: 1px solid #373737;
                border-radius: 4px;
                padding: 8px 12px;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2196F3;
            }
        """
        )
        self.timeSettingsBtn.setFixedSize(120, 36)
        self.timeSettingsBtn.clicked.connect(self.openTimeSettingsDialog)
        self.formLayoutContainerLayout.addWidget(self.timeSettingsBtn, 0, Qt.AlignCenter)

        self.contentLayout.addWidget(self.formLayoutContainer, 0, Qt.AlignCenter)

        # Link Ekle Butonu
        self.linkEkleBtn = QPushButton("Link Ekle")
        self.linkEkleBtn.setStyleSheet(
            """
            QPushButton {
                background-color: #242424;
                border: 1px solid #373737;
                border-radius: 4px;
                padding: 8px 12px;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2196F3;
            }
        """
        )
        self.linkEkleBtn.setFixedSize(90, 36)
        self.linkEkleBtn.clicked.connect(self.openLinkSettingsDialog)
        self.contentLayout.addWidget(self.linkEkleBtn, 0, Qt.AlignCenter)
        self.contentLayout.addSpacing(12)

        # ------------------ Alt Bar (Kaydet/Kapat) ------------------
        self.bottomButtonWidget = QWidget()
        self.bottomButtonWidget.setFixedHeight(60)
        self.bottomButtonWidget.setStyleSheet(
            """
            background-color: #2A2A2A;
            border-top: 1px solid #373737;
        """
        )
        self.bottomButtonLayout = QHBoxLayout(self.bottomButtonWidget)
        self.bottomButtonLayout.setContentsMargins(10, 10, 10, 10)
        self.bottomButtonLayout.setSpacing(0)

        self.saveBtn = QPushButton("Kaydet")
        self.closeBtn2 = QPushButton("Kapat")
        buttonStyle = """
            QPushButton {
                background-color: #242424;
                border: 1px solid #444;
                border-radius: 6px;
                padding: 10px 20px;
                color: #FFFFFF;
                font-size: 14px;
                font-weight: bold;
                min-width: 110px;
            }
            QPushButton:hover {
                background-color: #2196F3;
                border-color: #2196F3;
            }
        """
        self.saveBtn.setStyleSheet(buttonStyle)
        self.closeBtn2.setStyleSheet(buttonStyle)
        self.saveBtn.clicked.connect(self.saveProfile)
        self.closeBtn2.clicked.connect(self.close)

        self.bottomButtonLayout.addWidget(self.saveBtn, alignment=Qt.AlignLeft)
        self.bottomButtonLayout.addStretch()
        self.bottomButtonLayout.addWidget(self.closeBtn2, alignment=Qt.AlignRight)

        self.contentLayout.addWidget(self.bottomButtonWidget)

        self.mainLayout.addWidget(self.titleBar)
        self.mainLayout.addWidget(self.contentWidget)

        self.setStyleSheet(
            """
            QDialog {
                background-color: #2A2A2A;
                color: #FFFFFF;
                font-family: 'Segoe UI', sans-serif;
                border: 2px solid #2196F3;
                border-radius: 8px;
            }
            QFormLayout QLabel {
                color: #FFFFFF !important;
                font-size: 12px;
                font-weight: bold;
                font-family: 'Segoe UI', sans-serif;
            }
            QLineEdit {
                background-color: #2A2A2A;
                border: 1px solid #373737;
                border-radius: 4px;
                padding: 5px;
                color: #FFFFFF;
                font-size: 11px;
            }
            QPushButton {
                background-color: #2B2B2B;
                border: 1px solid #373737;
                border-radius: 4px;
                padding: 6px 13px;
                color: #FFFFFF;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2196F3;
                border-color: #2196F3;
            }
            """
        )

        # Sürükleme değişkenleri
        self.dragPos = None
        self.draggingTitleBar = False

        # TitleBar olaylarını yakalayalım
        self.titleBar.mousePressEvent = self._titleBarMousePress
        self.titleBar.mouseReleaseEvent = self._titleBarMouseRelease
        self.titleBar.mouseMoveEvent = self._titleBarMouseMove

    def centerDialog(self):
        if self.parent():
            parent_center = self.parent().frameGeometry().center()
            self.move(parent_center.x() - self.width() // 2, parent_center.y() - self.height() // 2)
        else:
            frame_geom = self.frameGeometry()
            screen_center = QDesktopWidget().availableGeometry().center()
            frame_geom.moveCenter(screen_center)
            self.move(frame_geom.topLeft())

    def _titleBarMousePress(self, event):
        if event.button() == Qt.LeftButton:
            self.dragPos = event.globalPos()
            self.draggingTitleBar = True
        event.accept()

    def _titleBarMouseRelease(self, event):
        self.dragPos = None
        self.draggingTitleBar = False
        event.accept()

    def _titleBarMouseMove(self, event):
        if self.draggingTitleBar and self.dragPos is not None:
            delta = event.globalPos() - self.dragPos
            new_pos = self.pos() + delta
            self.move(new_pos)
            self.dragPos = event.globalPos()
        event.accept()

    def onUsernameChanged(self, newText):
        newTextStripped = newText.strip()
        name_to_emit = ""

        if not newTextStripped:
            # For the dialog's own label, make it appear empty or show a placeholder
            self.profileNameLabel.setText(" ")  # Or a placeholder like "[Kullanıcı Adı Yok]"
            self.profileNameLabel.setStyleSheet("""
                background: transparent;
                border: 1px solid transparent; 
                border-radius: 6px;
                color: #888; 
                padding: 6px 12px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 16px;
                font-weight: bold;
                letter-spacing: 0.2px;
            """)
            name_to_emit = os.path.splitext(os.path.basename(self.profilePath))[0]
        else:
            self.profileNameLabel.setText(newTextStripped)
            self.profileNameLabel.setStyleSheet("""
                color: black;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #ffe082,
                    stop: 0.5 #fff9c4,
                    stop: 1 #ffd54f
                );
                border: 1px solid #d6a300;
                border-radius: 6px;
                padding: 6px 12px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 16px;
                font-weight: bold;
                letter-spacing: 0.2px;
            """)
            name_to_emit = newTextStripped

        self.profileNameLabel.adjustSize()
        self.profileNameChanged.emit(self.profilePath, name_to_emit)

    def saveProfile(self):
        self.data["username"] = self.usernameEdit.text().strip()
        self.data["password"] = self.passwordEdit.text().strip()

        # Kullanıcı adı veya şifre eksikse links listesini de temizle
        if not self.data["username"] or not self.data["password"]:
            self.data["links"] = []

        hashtags_text = self.hashtagsEdit.text().strip()
        if hashtags_text:
            self.data["hashtags"] = [tag.strip() for tag in hashtags_text.split(",")]
        else:
            self.data["hashtags"] = []

        try:
            with open(self.profilePath, "w", encoding="utf-8") as f:
                json.dump(self.data, f, indent=4)
            self.infoLabel.setText("KAYDEDİLDİ!")

            # Determine the name to display in the list after save
            saved_username = self.data["username"]
            final_display_name_for_list = ""
            if not saved_username:
                final_display_name_for_list = os.path.splitext(os.path.basename(self.profilePath))[0]
            else:
                final_display_name_for_list = saved_username

            # Emit the final name to update the list
            self.profileNameChanged.emit(self.profilePath, final_display_name_for_list)

            # Can barlarını ve istatistikleri anında güncelle
            if self.parent() and hasattr(self.parent(), 'update_stats'):
                self.parent().update_stats()

            if self.parent() and hasattr(self.parent(), 'reload_scheduler'):
                self.parent().reload_scheduler()
            # Removed redundant reload_scheduler call
            self.infoLabel.setStyleSheet("font-weight: bold; color: #39FF14; font-size: 15px;")
            QTimer.singleShot(2500, lambda: self.infoLabel.setText(""))
        except Exception as e:
            self.infoLabel.setText(f"Hata: {str(e)}")
            self.infoLabel.setStyleSheet("color: #FF0000; font-size: 13px;")

    def openTimeSettingsDialog(self):
        if getattr(self, 'timeSettingsDialog', None) is not None:
            self.timeSettingsDialog.close()
            self.timeSettingsDialog = None
        self.timeSettingsDialog = self.TimeSettingsDialog(self.data["schedule"], self)
        self.timeSettingsDialog.destroyed.connect(lambda: setattr(self, 'timeSettingsDialog', None))
        self.timeSettingsDialog.show()

    def openLinkSettingsDialog(self):
        if getattr(self, 'linkSettingsDialog', None) is not None:
            self.linkSettingsDialog.close()
            self.linkSettingsDialog = None
        self.linkSettingsDialog = self.LinkSettingsDialog(self.data, self)
        self.linkSettingsDialog.destroyed.connect(lambda: setattr(self, 'linkSettingsDialog', None))
        self.linkSettingsDialog.show()

    class TimeSettingsDialog(QDialog):
        """
        Saat ayarları penceresi (gün bazlı).
        Bu pencerede saatler, "21:43 - 23:41 - 23:55" formatında girilecektir.
        Yazı kutusuna girilen metin, satır sonuna gelince otomatik olarak aşağıya geçer.
        """

        def __init__(self, scheduleDict, parent=None):
            super().__init__(parent)
            self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
            self.setWindowModality(Qt.ApplicationModal)
            self.setModal(True)
            self.resize(400, 280)

            self.original_schedule = scheduleDict
            self.temp_schedule = json.loads(json.dumps(scheduleDict))
            self.changesSaved = False

            self.currentDay = "monday"

            shadow = QGraphicsDropShadowEffect(self)
            shadow.setOffset(0, 0)
            shadow.setBlurRadius(15)
            shadow.setColor(QColor(0, 0, 0, 180))
            self.setGraphicsEffect(shadow)

            self.mainLayout = QVBoxLayout(self)
            self.mainLayout.setContentsMargins(15, 15, 15, 15)
            self.mainLayout.setSpacing(12)

            self.titleLabel = QLabel("Saat Ayarları")
            self.titleLabel.setStyleSheet("""
                font-size: 14px;
                font-weight: bold;
                color: #FFFFFF;
                margin-bottom: 5px;
            """)
            self.titleLabel.setAlignment(Qt.AlignCenter)
            self.mainLayout.addWidget(self.titleLabel)

            self.statusLabel = QLabel("")
            self.statusLabel.setStyleSheet("font-size: 12px; color: #39FF14;")
            self.statusLabel.setAlignment(Qt.AlignCenter)
            self.mainLayout.addWidget(self.statusLabel)

            self.daySelector = DaySelector(self)
            self.mainLayout.addWidget(self.daySelector, 0, Qt.AlignCenter)
            self.daySelector.setCurrentDay(self.currentDay)
            self.daySelector.dayChanged.connect(self.onDayChanged)

            self.textEdit = QPlainTextEdit(self)
            self.textEdit.setPlaceholderText("Örnek: 21:43 - 23:41 - 23:55")
            self.textEdit.setStyleSheet("""
                background-color: #1E1E1E;
                border: 1px solid #373737;
                border-radius: 4px;
                color: #FFFFFF;
                font-size: 12px;
                font-family: 'Segoe UI';
                padding: 8px;
                selection-background-color: #2196F3;
                selection-color: #FFFFFF;
            """)
            self.textEdit.setLineWrapMode(QPlainTextEdit.WidgetWidth)
            self.mainLayout.addWidget(self.textEdit, 1)

            self.buttonLayout = QHBoxLayout()
            self.saveDayBtn = QPushButton("Kaydet")
            self.closeBtn = QPushButton("Kapat")

            buttonStyle = """
                QPushButton {
                    background-color: #2B2B2B;
                    border: 1px solid #373737;
                    border-radius: 4px;
                    padding: 6px 12px;
                    color: #FFFFFF;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #2196F3;
                    border-color: #2196F3;
                }
            """
            self.saveDayBtn.setStyleSheet(buttonStyle)
            self.closeBtn.setStyleSheet(buttonStyle)

            self.saveDayBtn.clicked.connect(self.onSaveClicked)
            self.closeBtn.clicked.connect(self.close)

            self.buttonLayout.addStretch()
            self.buttonLayout.addWidget(self.saveDayBtn)
            self.buttonLayout.addWidget(self.closeBtn)
            self.buttonLayout.addStretch()
            self.mainLayout.addLayout(self.buttonLayout)

            self.setStyleSheet("""
                QDialog {
                    background-color: #2A2A2A;
                    color: #FFFFFF;
                    border: 1px solid #2196F3;
                    border-radius: 6px;
                }
            """)

            self.dragPos = None
            self.loadTimesForDay(self.currentDay)

        def mousePressEvent(self, event):
            if event.button() == Qt.LeftButton:
                self.dragPos = event.globalPos()
            super().mousePressEvent(event)

        def mouseReleaseEvent(self, event):
            self.dragPos = None
            super().mouseReleaseEvent(event)

        def mouseMoveEvent(self, event):
            if event.buttons() == Qt.LeftButton and self.dragPos is not None:
                delta = event.globalPos() - self.dragPos
                self.move(self.pos() + delta)
                self.dragPos = event.globalPos()
            super().mouseMoveEvent(event)

        def onDayChanged(self, newDay):
            self.saveCurrentDayTimes()
            self.currentDay = newDay
            self.loadTimesForDay(newDay)
            self.statusLabel.setText("")

        def loadTimesForDay(self, day):
            timesList = self.temp_schedule.get(day, [])
            self.textEdit.clear()
            if timesList:
                self.textEdit.setPlainText(" - ".join(timesList))

        def saveCurrentDayTimes(self):
            raw_text = self.textEdit.toPlainText().strip()
            if not raw_text:
                self.temp_schedule[self.currentDay] = []
            else:
                times = []
                for line in raw_text.split("\n"):
                    parts = line.split(" - ")
                    for part in parts:
                        t = part.strip()
                        if t:
                            times.append(t)
                self.temp_schedule[self.currentDay] = times

        def onSaveClicked(self):
            self.saveCurrentDayTimes()
            self.original_schedule.clear()
            self.original_schedule.update(self.temp_schedule)
            self.changesSaved = True
            self.statusLabel.setText("<b>KAYDEDİLDİ!</b>")
            QTimer.singleShot(2000, lambda: self.statusLabel.setText(""))

        def closeEvent(self, event):
            super().closeEvent(event)

    class LinkSettingsDialog(QDialog):
        """
        Link Ekleme Penceresi (gün bazlı değil).
        Tüm linkler tek listede saklanır (self.data["links"]).
        """

        def __init__(self, data, parent=None):
            super().__init__(parent)
            self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
            self.setWindowModality(Qt.ApplicationModal)
            self.setModal(True)
            self.resize(400, 280)

            self.data = data

            shadow = QGraphicsDropShadowEffect(self)
            shadow.setOffset(0, 0)
            shadow.setBlurRadius(15)
            shadow.setColor(QColor(0, 0, 0, 180))
            self.setGraphicsEffect(shadow)

            self.mainLayout = QVBoxLayout(self)
            self.mainLayout.setContentsMargins(15, 15, 15, 15)
            self.mainLayout.setSpacing(12)

            self.titleLabel = QLabel("Link Ekleme")
            self.titleLabel.setStyleSheet("""
                font-size: 14px;
                font-weight: bold;
                color: #FFFFFF;
                margin-bottom: 5px;
            """)
            self.titleLabel.setAlignment(Qt.AlignCenter)
            self.mainLayout.addWidget(self.titleLabel)

            self.statusLabel = QLabel("")
            self.statusLabel.setStyleSheet("font-size: 12px; color: #39FF14;")
            self.statusLabel.setAlignment(Qt.AlignCenter)
            self.mainLayout.addWidget(self.statusLabel)

            self.textEdit = QPlainTextEdit(self)
            self.textEdit.setPlaceholderText(
                "Her satıra bir link ekleyin.\nÖrnek:\nhttps://youtu.be/... \nhttps://twitter.com/...")
            self.textEdit.setStyleSheet("""
                background-color: #1E1E1E;
                border: 1px solid #373737;
                border-radius: 4px;
                color: #FFFFFF;
                font-size: 12px;
                font-family: 'Segoe UI';
                padding: 8px;
                selection-background-color: #2196F3;
                selection-color: #FFFFFF;
            """)
            self.textEdit.setLineWrapMode(QPlainTextEdit.WidgetWidth)
            self.mainLayout.addWidget(self.textEdit, 1)

            # Daha önce indirilen linkleri çıkar
            existing_links = self.data.get("links", [])
            downloaded_links = {item["url"] for item in self.data.get("downloaded", []) if
                                isinstance(item, dict) and "url" in item}
            filtered_links = [link for link in existing_links if link not in downloaded_links]
            if filtered_links:
                self.textEdit.setPlainText("\n".join(filtered_links))

            self.buttonLayout = QHBoxLayout()
            self.saveBtn = QPushButton("Kaydet")
            self.closeBtn = QPushButton("Kapat")

            buttonStyle = """
                QPushButton {
                    background-color: #2B2B2B;
                    border: 1px solid #373737;
                    border-radius: 4px;
                    padding: 6px 12px;
                    color: #FFFFFF;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #2196F3;
                    border-color: #2196F3;
                }
            """
            self.saveBtn.setStyleSheet(buttonStyle)
            self.closeBtn.setStyleSheet(buttonStyle)

            self.saveBtn.clicked.connect(self.onSaveClicked)
            self.closeBtn.clicked.connect(self.close)

            self.buttonLayout.addStretch()
            self.buttonLayout.addWidget(self.saveBtn)
            self.buttonLayout.addWidget(self.closeBtn)
            self.buttonLayout.addStretch()

            self.mainLayout.addLayout(self.buttonLayout)

            self.setStyleSheet("""
                QDialog {
                    background-color: #2A2A2A;
                    color: #FFFFFF;
                    border: 1px solid #2196F3;
                    border-radius: 6px;
                }
            """)

            self.dragPos = None

        def mousePressEvent(self, event):
            if event.button() == Qt.LeftButton:
                self.dragPos = event.globalPos()
            super().mousePressEvent(event)

        def mouseReleaseEvent(self, event):
            self.dragPos = None
            super().mouseReleaseEvent(event)

        def mouseMoveEvent(self, event):
            if event.buttons() == Qt.LeftButton and self.dragPos is not None:
                delta = event.globalPos() - self.dragPos
                self.move(self.pos() + delta)
                self.dragPos = event.globalPos()
            super().mouseMoveEvent(event)

        def onSaveClicked(self):
            # Mevcut metin kutusundan linkleri alıp data["links"] içine koyacağız
            raw_text = self.textEdit.toPlainText().strip()
            if not raw_text:
                self.data["links"] = []
            else:
                lines = [line.strip() for line in raw_text.split("\n") if line.strip()]
                self.data["links"] = lines

            self.statusLabel.setText("<b>KAYDEDİLDİ!</b>")

            # 2 saniye sonra mesajı kaldır
            QTimer.singleShot(2000, lambda: self.statusLabel.setText(""))

            # Can barlarını güncelle
            try:
                from PyQt5.QtWidgets import QApplication
                for widget in QApplication.topLevelWidgets():
                    if hasattr(widget, "update_stats"):
                        widget.update_stats()
                        break
            except Exception as e:
                print(f"Can barları güncellenemedi: {e}")

        def closeEvent(self, event):
            # Kapatırken en son durumu kaydedelim
            self.onSaveClicked()
            super().closeEvent(event)

    def profil_sil(self):
        """
        Bu fonksiyon, mevcut profilin JSON dosyasını ve istatistiklerini tamamen siler.
        """
        try:
            # Profilin istatistik anahtarını oluşturmak için önce username ve platformu oku
            if os.path.exists(self.profilePath):
                with open(self.profilePath, "r", encoding="utf-8") as f:
                    data = json.load(f)
                username = data.get("username", "").strip()
                base_dir = os.path.dirname(os.path.dirname(self.profilePath))
                platform = os.path.basename(base_dir)

                # Profil anahtar ismini oluştur
                profile_key = f"{platform}_{username}" if username else None

                # Profil dosyasını sil
                os.remove(self.profilePath)
                logging.info(f"Profil dosyası silindi: {self.profilePath}")

                # Profilin istatistiklerini temizle
                if username and profile_key:
                    # Doğrudan ana stats_system nesnesini kullan
                    from Sorcerio1_7 import stats_system

                    # İstatistik verilerini temizle
                    if profile_key in stats_system.stats_data:
                        del stats_system.stats_data[profile_key]
                    if profile_key in stats_system.last_stats_update:
                        del stats_system.last_stats_update[profile_key]
                    if profile_key in stats_system.stats_display_queue:
                        stats_system.stats_display_queue.remove(profile_key)
                    if profile_key in stats_system.profiles_to_process:
                        stats_system.profiles_to_process.discard(profile_key)

                    # Eğer silinen profil şu an gösterilen profilse, başka bir profile geç
                    if stats_system.current_displayed_profile == profile_key:
                        stats_system.current_displayed_profile = next(iter(stats_system.stats_display_queue), None)

                    # İstatistikleri dosyaya kaydet
                    stats_system.istatistikleri_kaydet()

                    logging.info(f"{profile_key} için istatistik verileri profil silme işlemi sırasında temizlendi.")

            # Silme sonrası her iki deck'i de anında ve UI thread'inde güncelle
            from PyQt5.QtCore import QTimer
            if self.parent() and hasattr(self.parent(), 'update_stats'):
                # Profil silme sonrası UI'ı hemen güncelle
                QTimer.singleShot(0, lambda: self.profile_silme_ui_guncelle(self.parent()))
                logging.info("Profil silme sonrası UI anında güncellendi.")
        except Exception as e:
            logging.error(f"Profil silinirken hata: {str(e)}")

    def profile_silme_ui_guncelle(self, parent_window):
        """
        Profil silme sonrası UI'ı güncelleyen yardımcı fonksiyon
        """
        try:
            # Ana pencerede update_stats fonksiyonunu çağır
            if hasattr(parent_window, 'update_stats'):
                parent_window.update_stats()

            # Profil listesini yeniden yükle (sol deck'i güncelle)
            parent_window.view.page().runJavaScript('''
                if (typeof refreshProfilesList === 'function') {
                    refreshProfilesList();
                }
            ''')
        except Exception as e:
            logging.error(f"Profil silme sonrası UI güncellenirken hata: {str(e)}")


class DaySelector(QWidget):
    """
    Basit bir gün seçici: 7 gün (monday..sunday) için buton dizisi.
    Sinyalle currentDay'i dışarı iletir.
    """
    dayChanged = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.currentDay = "monday"
        self.days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
        self.displayNames = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]

        self.layout = QHBoxLayout(self)
        self.layout.setSpacing(5)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.buttons = []

        for i, day in enumerate(self.days):
            btn = QPushButton(self.displayNames[i])
            btn.setCheckable(True)
            btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #242424;
                    border: 1px solid #373737;
                    border-radius: 4px;
                    padding: 6px 10px;
                    color: #FFFFFF;
                    font-size: 11px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2196F3;
                }
                QPushButton:checked {
                    background-color: #2196F3;
                }
                """
            )
            btn.clicked.connect(self.onDayButtonClicked)
            self.layout.addWidget(btn)
            self.buttons.append(btn)

        self.updateButtonStates()

    def setCurrentDay(self, day):
        if day in self.days:
            self.currentDay = day
        self.updateButtonStates()

    def onDayButtonClicked(self):
        sender = self.sender()
        if sender in self.buttons:
            index = self.buttons.index(sender)
            self.currentDay = self.days[index]
            self.updateButtonStates()
            self.dayChanged.emit(self.currentDay)

    def updateButtonStates(self):
        for i, day in enumerate(self.days):
            self.buttons[i].setChecked(day == self.currentDay)


class ProfileBridge(QObject):
    """
    Profil listesi oluşturma ve profil düzenleme diyalogunu açma köprüsü.
    Sol deck'te gösterilecek isimler, JSON içindeki 'username' varsa onu,
    aksi halde dosya adını gösterir.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.profileEditorDialog = None
        self.linkEditorDialog = None

    @pyqtSlot(str)
    def openLinkEditorOnly(self, profilePath):
        if getattr(self, 'linkEditorDialog', None) is not None:
            self.linkEditorDialog.close()
            self.linkEditorDialog = None
        try:
            with open(profilePath, "r", encoding="utf-8") as f:
                data = json.load(f)
            self.linkEditorDialog = ProfileEditorDialog.LinkSettingsDialog(data, self.parent())
            self.linkEditorDialog.destroyed.connect(lambda: setattr(self, 'linkEditorDialog', None))
            self.linkEditorDialog.show()
        except Exception as e:
            print(f"Link Ekleme Penceresi açılamadı: {e}")

    @pyqtSlot(str, result=str)
    def getProfiles(self, platform):
        base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
        config_dir = os.path.join(base_dir, "configuration", platform)
        profiles = []
        if os.path.exists(config_dir):
            for file in os.listdir(config_dir):
                if file.endswith(".json"):
                    path = os.path.join(config_dir, file)
                    display_name = file
                    try:
                        with open(path, "r", encoding="utf-8") as f:
                            data = json.load(f)
                        username = data.get("username", "").strip()
                        if username:
                            display_name = username
                        else:
                            display_name = os.path.splitext(file)[0]  # dosya adını göster (örnek: profile1)
                    except:
                        pass
                    profiles.append(
                        {
                            "path": path,
                            "displayName": display_name,
                        }
                    )
        return json.dumps(profiles)

    @pyqtSlot(str)
    def openProfileEditor(self, profilePath):
        if getattr(self, 'profileEditorDialog', None) is not None:
            self.profileEditorDialog.close()
            self.profileEditorDialog = None
        self.profileEditorDialog = ProfileEditorDialog(profilePath, self.parent())
        self.profileEditorDialog.profileNameChanged.connect(self.updateProfileNameInList)
        self.profileEditorDialog.destroyed.connect(lambda: setattr(self, 'profileEditorDialog', None))
        self.profileEditorDialog.show()

    @pyqtSlot(str, str)
    def updateProfileNameInList(self, profilePath, newName):
        mainWindow = self.parent()
        if mainWindow:
            mainWindow.view.page().runJavaScript(
                f'window.updateProfileItemText({json.dumps(profilePath)}, {json.dumps(newName)});'
            )


html_content = r"""
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>3 Deck UI - Dark Theme (Modernized)</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
    .skeleton {
    background: linear-gradient(-90deg, #2b2b2b 0%, #3a3a3a 50%, #2b2b2b 100%);
    background-size: 400% 400%;
    animation: shimmer 1.2s ease-in-out infinite;
    border-radius: 10px;
    height: 20px;
    width: 80%;
    margin: 6px auto;
}


@keyframes shimmer {
    0% { background-position: 100% 0; }
    100% { background-position: -100% 0; }
}

.skeleton-container {
    display: flex;
    flex-direction: column;
    gap: 14px;
    padding: 10px 5px;
}


    /* ==== GENEL KUTU DİZİLİMİ ==== */
    .stats-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr); /* Her kutu tek satır kaplar */
    gap: 10px;
}

.stat-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #1e1e1e;
    padding: 10px 15px;
    border-radius: 10px;
}



    /* ==== TWITTER KUTULARI ==== */
    .twitter-stats .stat-box {
        width: 100%;                    /* Hücrenin tamamını kapsar */
        height: auto;
        font-size: 13px;
        padding: 10px;
        background-color: #1da1f2;
        color: white;
        border-radius: 10px;
        text-align: center;
        box-sizing: border-box;         /* İçerik dışarı taşmasın */
    }


    .instagram-stats .stat-box {
        width: 100%;
        height: auto;
        font-size: 13px;               /* Aynı font boyutu */
        padding: 10px;                 /* Twitter'da 10, Instagram'da 10px 12px idi => eşitledik */
        background-color: #1E1E1E;     /* İsterseniz #1da1f2 yapıp aynı renge getirebilirsiniz */
        color: #FFFFFF;
        border-radius: 10px;
        text-align: center;            /* Twitter'daki gibi ortalarsanız aynı görünür */
        box-sizing: border-box;
    }




    .stat-label {
        font-size: 14px;
        color: #ccc;
    }

    .stat-right {
        text-align: right;
    }

    .stat-value {
        font-size: 18px;
        font-weight: bold;
        color: #fff;
    }

    .instagram-stats .stat-label {
        font-size: 14px;
        color: #ccc;
        text-align: left;
    }
    .instagram-stats .stat-value {
        font-size: 16px;
        font-weight: bold;
        color: #fff;
        text-align: left;
    }



    .stat-change {
        font-size: 13px;
        margin-top: 2px;
    }



    .stat-change.up {
        color: #00ff88;
    }

    .stat-change.down {
        color: #ff5555;
    }
        :root {
            --bg-color: #121212;
            --card-bg: #1E1E1E;
            --text-primary: #FFFFFF;
            --text-secondary: #B3B3B3;
            --accent-blue: #2196F3;
            --border-color: #373737;
            --base-font-size: 14px;
            --section-header-size: 13px;
            --deck-padding: 12px;
            --gap-size: 10px;
            --title-bar-height: 36px;

            /* 1) Üst ve alt bar rengini biraz daha açtık (örnek: #3C3C3C) */
            --header-footer-bg: #3C3C3C;
        }
        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            background-color: var(--bg-color);
            font-family: 'Segoe UI', sans-serif;
            color: var(--text-primary);
            overflow: hidden;
        }

        .title-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--title-bar-height);
            background-color: var(--header-footer-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            z-index: 999;
            -webkit-user-select: none;
            cursor: default;
        }

        .status-indicator-bar {
            position: absolute;
            top: 9px; /* ¡¡¡ Aşağıya indirildi */
            left: 50%;
            transform: translateX(-50%);
            width: 100px;  /* ? Daha geniş */
            height: 20px;  /* ^ Daha yüksek */
            background-color: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
            z-index: 9999;
        }

        .status-light-bar {
            width: 100px;
            height: 20px;
            border-radius: 10px;
            box-shadow: 0 0 4px #00000055;
            background-size: 200% 100%;
        }



        @keyframes idleAnim {
            0% { background-position: 100% 0; }
            100% { background-position: -100% 0; }
        }

        @keyframes activeAnim {
            0% { background-position: 0% 50%; background-color: #2ecc71; }
            100% { background-position: 100% 50%; background-color: #27ae60; }
        }

        @keyframes stopAnim {
            0% { background-position: 0% 50%; background-color: #e74c3c; }
            100% { background-position: 100% 50%; background-color: #c0392b; }
        }

        /* YENİ EKLENEN - Akış animasyonları */
        @keyframes flowGreen {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        @keyframes flowRed {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        /* --- YENİ EKLENEN SONU --- */





        .window-title {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg,
                #FFD700 0%,
                #FFD700 45%,
                #FFFFFF 45%,
                #FFFFFF 55%,
                #FFD700 55%,
                #FFD700 100%
            );
            background-size: 200%;
            background-repeat: repeat;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shine 3s infinite alternate;
        }



        @keyframes shine {
            0% {
                background-position: 0% 0;
                animation-timing-function: linear;
            }
            80% {
                background-position: 90% 0;
                animation-timing-function: ease-out;
            }
            100% {
                background-position: 100% 0;
            }
        }

        .window-buttons {
            display: flex;
            gap: 6px;
        }
        .window-button {
            width: 24px;
            height: 24px;
            border: none;
            border-radius: 4px;
            background: var(--border-color);
            color: var(--text-primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            -webkit-user-select: none;
        }
        .window-button:hover {
            background: var(--accent-blue);
            color: #fff;
        }

        #close-btn:hover {
            background-color: #D32F2F !important;
            color: #fff !important;
        }


        .extra-buttons {
            display: flex;
            gap: 20px;
            margin-left: auto;
            margin-right: 40px;
        }
        .title-bar-button {
            padding: 5px 12px;
            background: #E0E0E0; /* pastel açık renk */
            border: none;
            border-radius: 4px;
            color: #333; /* koyu yazı */
            font-size: 12px;
            cursor: pointer;
        }
        #signup-btn {
            margin-right: 10px;
        }
        .title-bar-button:hover {
            background: #d5d5d5;
        }

        .grid-container {
            position: absolute;
            top: calc(var(--title-bar-height) + 10px);
            left: var(--gap-size);
            right: 70px; /* en sağdaki bar için boşluk */
            bottom: 60px; /* alt barda 60px yer var */
            display: grid;
            /* sol deck'i biraz genişlettik: 0.64fr */
            grid-template-columns: 0.64fr 4px 1fr 4px 1fr 4px 1fr;
            grid-template-rows: 1fr;
            gap: var(--gap-size);
        }
        .resizer {
            background: var(--border-color);
            cursor: col-resize;
            width: 4px;
        }
        .resizer:hover {
            background: var(--accent-blue);
        }
        .deck {
            background: var(--card-bg);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: var(--deck-padding);
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) var(--bg-color);
        }
            .deck > * {
            transition: all 0.4s ease-in-out;
        }

        .deck::-webkit-scrollbar {
            width: 10px;
            background-color: var(--bg-color);
        }
        .deck::-webkit-scrollbar-track {
            background-color: var(--bg-color);
        }
        .deck::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border-radius: 6px;
            border: 2px solid var(--bg-color);
        }
        .deck::-webkit-scrollbar-thumb:hover {
            background-color: var(--accent-blue);
        }
        .left-deck {
            display: flex;
            flex-direction: column;
            gap: var(--gap-size);
        }
        .profile-selector {
            margin-bottom: 12px;
        }
        .selector-buttons {
            margin-bottom: 12px;
        }
        .platform-select {
            width: 100%;
            padding: 8px;
            border: 1px solid #B0B0B0;
            border-radius: 5px;
            background-color: #F5F5F7;
            color: #000000;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
        }
        .platform-select option {
            background-color: #F5F5F7;
            color: #000000;
            font-weight: bold;
        }
        .platform-select option:hover {
            background-color: #E0E0E5;
            color: #000000;
        }
        .profile-list {
            margin-top: 6px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .profile-item {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
            font-size: 13px;
            text-align: center;
        }

        .profile-item:hover {
            background: var(--accent-blue);
        }
        .middle-deck {}
        .tab-bar {
            display: flex;
            gap: 12px;
            margin-bottom: 10px;
        }
        .tab {
            color: var(--text-secondary);
            cursor: pointer;
            padding-bottom: 2px;
            font-weight: 500;
            font-size: 14px;
        }
        .tab.active {
            color: var(--text-primary);
            border-bottom: 2px solid var(--accent-blue);
        }
        .list-item {
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }
        .list-item:last-child {
            border-bottom: none;
        }
        .thread-count {
            background: var(--accent-blue);
            color: white;
            padding: 2px 4px;
            border-radius: 4px;
            font-size: 11px;
        }

        .right-deck {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .section {
            margin-bottom: 14px;
        }
        .section-header {
            font-size: var(--section-header-size);
            color: var(--text-secondary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .badge {
            background: #373737;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        ul {
            list-style: none;
            padding-left: 0;
            margin: 6px 0;
        }
        ul li {
            position: relative;
            padding-left: 16px;
            margin-bottom: 4px;
            font-size: 14px;
        }
        ul li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: var(--accent-blue);
        }

        .bottom-bar {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--header-footer-bg);
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            height: 50px;
        }
        .bottom-buttons {
            display: flex;
            gap: 12px;
        }
        .action-button {
            background: #292A2D;
            border: 1px solid #3A3B3E;
            padding: 8px 14px;
            border-radius: 6px;
            color: #F8F8F8;
            font-size: 14px;
            font-family: 'Segoe UI', sans-serif;
            letter-spacing: 0.4px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s, color 0.3s, border-color 0.3s;
        }
        .action-button:hover {
            background: #35363A;
            color: #FFFFFF;
            border-color: #505255;
        }
        .action-button.primary {
            background: #006400;
            color: #EAEAEA;
            font-weight: bold;
            font-size: 15px;
            letter-spacing: 0.6px;
            border: 1px solid #163A5E;
        }
        .action-button.primary:hover {
            background: #008f5a; /* daha koyu, yumuşak bir yeşil */
            border-color: #267f60; /* daha yumuşak bir kontrast */
            color: #FFFFFF;
        }

        #stopBtn {
            background: #5E1E1E;
            border: 1px solid #4A1919;
            color: #FFFFFF;
        }
        #stopBtn:hover {
            background: #C0392B;
            border-color: #A93226;
            color: #FFFFFF;
        }

        .right-side-bar {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            width: 50px;
            background-color: var(--header-footer-bg);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 45px;
        }
        .right-side-bar-button {
            font-size: 20px;
            background: none;
            border: none;
            color: var(--text-primary);
            cursor: pointer;
            margin-bottom: 8px;
        }
        .right-side-bar-button:hover {
            color: var(--accent-blue);
        }

        /* -- İSTATİSTİK KUTUCUKLARI İÇİN YENİ CSS -- */
.stats-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.stats-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
    color: #fff;
}

.stats-update-time {
    font-size: 12px;
    color: #aaa;
}

/* === GRID DÜZENİ === */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr); /* Her kutu tek satır kaplar */
    gap: 10px;
}

.stat-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #1e1e1e;
    padding: 10px 15px;
    border-radius: 10px;
}


.stat-label {
    font-size: 13px;
    color: #aaa;
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
}

.stat-change {
    font-size: 12px;
    font-weight: bold;
}

/* Artış = yeşil */
.stat-change.up {
    color: #4caf50;
}

/* Düşüş = kırmızı */
.stat-change.down {
    color: #f44336;
}


        .extra-deck {
            background: var(--card-bg);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: var(--deck-padding);
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="title-bar">
        <div class="window-title">sorcerio</div>

    <div class="status-indicator-bar">
      <div class="status-light-bar" id="statusLight" style="animation: stopAnim 1.5s ease-in-out infinite;"></div>
    </div>

        <div class="window-buttons">
            <button class="window-button" id="min-btn" title="Simge Durumuna Küçült">–</button>
            <button class="window-button" id="max-btn" title="Büyüt">&#9744;</button> <!-- Unicode: ? -->
            <button class="window-button" id="close-btn" title="Kapat">&#10005;</button> <!-- Unicode: ? -->
        </div>
    </div>

    <div class="grid-container">
    <div class="deck left-deck">
        <div class="profile-selector">
            <div class="selector-buttons">
                <select id="platformSelect" class="platform-select">
                    <option value="instagram">Instagram</option>
                    <option value="twitter">Twitter</option>
                </select>
            </div>
            <div id="profile-list" class="profile-list"></div>
        </div>
    </div>

    <script>

    </script>


        <div class="resizer" id="resizer-1"></div>

        <div class="deck middle-deck">
    <div class="skeleton-container" id="middle-skeleton">
    <div class="skeleton" style="width: 80%; height: 24px;"></div>
    <div class="skeleton" style="width: 60%; height: 20px;"></div>
    <div class="skeleton" style="width: 90%; height: 24px;"></div>
    <div class="skeleton" style="width: 70%; height: 18px;"></div>
    </div>
</div>



        <div class="resizer" id="resizer-2"></div>

        <div class="deck right-deck">
        <div class="skeleton-container" id="right-skeleton">
    <div class="skeleton" style="width: 85%; height: 45px;"></div>
    <div class="skeleton" style="width: 80%; height: 45px;"></div>
    <div class="skeleton" style="width: 75%; height: 45px;"></div>
</div>


    <!-- Twitter istatistik kutuları -->
    <div class="stats-grid twitter-stats">
        <!-- Twitter kutuları burada oluşturulacak -->
    </div>

    <!-- Instagram istatistik kutuları -->
    <div class="stats-grid instagram-stats">
        <!-- Instagram kutuları burada oluşturulacak -->
    </div>
</div>


        <div class="resizer" id="resizer-3"></div>

        <div class="deck extra-deck">
    <div class="skeleton-container" id="extra-skeleton">
        <div class="skeleton" style="height: 20px; width: 70%;"></div>
        <div class="skeleton" style="height: 14px; width: 100%;"></div>
        <div class="skeleton" style="height: 14px; width: 90%;"></div>
        <div class="skeleton" style="height: 14px; width: 80%;"></div>
    </div>
</div>

    </div>

    <!-- Orijinal bottom-bar bloğunu bu güncellenmiş haliyle değiştirin -->
    <div class="bottom-bar">
      <div class="bottom-buttons">
        <button class="action-button primary" id="startBtn">Başla</button>
        <button class="action-button"        id="stopBtn">Durdur</button>
        <button class="action-button"        id="errorBtn">DESTEK</button>
      </div>
    </div>



    <div class="right-side-bar">
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            new QWebChannel(qt.webChannelTransport, function(channel) {
                let pyBridge = channel.objects.pyBridge;
                let profileBridge = channel.objects.profileBridge;

                function setStatusBarColor(colorState) {
                    const bar = document.querySelector(".status-light-bar");
                    if (!bar) return;

                    // Mevcut animasyonları sıfırla
                    bar.style.animation = 'none';
                    bar.style.backgroundImage = 'none';

                    let gradient = '';
                    let animationName = '';
                    let duration = '1.8s';

                    if (colorState === "working") {
                        // Daha keskin geçişli yeşil akış
                        gradient = 'linear-gradient(90deg, #1e8e3e 0%, #1e8e3e 40%, #34c759 50%, #1e8e3e 60%, #1e8e3e 100%)';
                        animationName = 'flowGreen';
                    } else if (colorState === "stopped") {
                        // Daha keskin geçişli kırmızı akış
                        gradient = 'linear-gradient(90deg, #a02c2c 0%, #a02c2c 40%, #e74c3c 50%, #a02c2c 60%, #a02c2c 100%)';
                        animationName = 'flowRed';
                    } else {
                        // Gri idle akışı
                        gradient = 'linear-gradient(90deg, #444 0%, #444 40%, #666 50%, #444 60%, #444 100%)';
                        duration = '2.5s';
                    }

                    bar.style.backgroundImage = gradient;
                    if (animationName) {
                        bar.style.animation = `${animationName} ${duration} linear infinite`;
                    }

                    // Animasyonu tetikle
                    void bar.offsetWidth;
                }




                // Sayfa yüklendiğinde başlangıçta kırmızı göster
                setStatusBarColor("stopped");



                // Pencere kontrol butonları
                document.getElementById("min-btn").addEventListener("click", function() {
                    pyBridge.minimizeWindow();
                });
                document.getElementById("max-btn").addEventListener("click", function() {
                    pyBridge.maximizeWindow();
                });
                document.getElementById("close-btn").addEventListener("click", function() {
                    pyBridge.closeWindow();
                });
                document.getElementById("startBtn").addEventListener("click", function() {
                  pyBridge.startProcessingFromJS();
                  setStatusBarColor("working");
                });

                document.getElementById("stopBtn").addEventListener("click", function() {
                  pyBridge.stopProcessing();
                  setStatusBarColor("stopped");
                });






                // Başlık çubuğu sürükleme
                let titleBar = document.querySelector(".title-bar");
                let isDraggingWindow = false;
                titleBar.addEventListener("mousedown", function(e) {
                    if (e.target.closest('.window-buttons') || e.target.closest('.extra-buttons')) {
                        return;
                    }
                    isDraggingWindow = true;
                    pyBridge.startDrag();
                });
                document.addEventListener("mouseup", function(e) {
                    if (isDraggingWindow) {
                        isDraggingWindow = false;
                        pyBridge.stopDrag();
                    }
                });
                document.addEventListener("mousemove", function(e) {
                    if (isDraggingWindow) {
                        pyBridge.dragWindow();
                    }
                });

                // Resizerlar
                const resizer1 = document.getElementById('resizer-1');
                const resizer2 = document.getElementById('resizer-2');
                const resizer3 = document.getElementById('resizer-3');
                const gridContainer = document.querySelector('.grid-container');
                let isResizing = false;
                let currentResizer = null;
                let startX = 0;
                let leftColWidth = 0;
                let middleColWidth = 0;
                let rightColWidth = 0;
                let extraColWidth = 0;

                function getPxWidths() {
                    const leftDeck = document.querySelector('.left-deck');
                    const middleDeck = document.querySelector('.middle-deck');
                    const rightDeck = document.querySelector('.right-deck');
                    const extraDeck = document.querySelector('.extra-deck');
                    let leftRect = leftDeck.getBoundingClientRect();
                    let midRect = middleDeck.getBoundingClientRect();
                    let rightRect = rightDeck.getBoundingClientRect();
                    let extraRect = extraDeck.getBoundingClientRect();
                    leftColWidth = leftRect.width;
                    middleColWidth = midRect.width;
                    rightColWidth = rightRect.width;
                    extraColWidth = extraRect.width;
                }
                function setGridColumns(l, m, r, e) {
                    gridContainer.style.gridTemplateColumns = l + 'px 4px ' + m + 'px 4px ' + r + 'px 4px ' + e + 'px';
                }
                function mouseDownHandler(e, whichResizer) {
                    isResizing = true;
                    currentResizer = whichResizer;
                    startX = e.clientX;
                    getPxWidths();
                    document.addEventListener('mousemove', mouseMoveHandler);
                    document.addEventListener('mouseup', mouseUpHandler);
                    e.preventDefault();
                }
                function mouseMoveHandler(e) {
                    if (!isResizing) return;
                    let dx = e.clientX - startX;
                    let minW = 100;
                    if (currentResizer === resizer1) {
                        let newLeft = leftColWidth + dx;
                        let newMid = middleColWidth - dx;
                        if (newLeft < minW) {
                            newLeft = minW;
                            newMid = leftColWidth + middleColWidth - minW;
                        }
                        if (newMid < minW) {
                            newMid = minW;
                            newLeft = leftColWidth + middleColWidth - minW;
                        }
                        setGridColumns(newLeft, newMid, rightColWidth, extraColWidth);
                    } else if (currentResizer === resizer2) {
                        let newMid = middleColWidth + dx;
                        let newRight = rightColWidth - dx;
                        if (newMid < minW) {
                            newMid = minW;
                            newRight = middleColWidth + rightColWidth - minW;
                        }
                        if (newRight < minW) {
                            newRight = minW;
                            newMid = middleColWidth + rightColWidth - minW;
                        }
                        setGridColumns(leftColWidth, newMid, newRight, extraColWidth);
                    } else if (currentResizer === resizer3) {
                        let newRight = rightColWidth + dx;
                        let newExtra = extraColWidth - dx;
                        if (newRight < minW) {
                            newRight = minW;
                            newExtra = rightColWidth + extraColWidth - minW;
                        }
                        if (newExtra < minW) {
                            newExtra = minW;
                            newRight = rightColWidth + extraColWidth - minW;
                        }
                        setGridColumns(leftColWidth, middleColWidth, newRight, newExtra);
                    }
                }
                function mouseUpHandler() {
                    isResizing = false;
                    currentResizer = null;
                    document.removeEventListener('mousemove', mouseMoveHandler);
                    document.removeEventListener('mouseup', mouseUpHandler);
                }
                resizer1.addEventListener('mousedown', (e) => mouseDownHandler(e, resizer1));
                resizer2.addEventListener('mousedown', (e) => mouseDownHandler(e, resizer2));
                resizer3.addEventListener('mousedown', (e) => mouseDownHandler(e, resizer3));

                // Platform seçimi (dropdown)
                const platformSelect = document.getElementById("platformSelect");
                const profileListDiv = document.getElementById("profile-list");

                window.profileItems = [];

                function updateProfileList(response) {
                    // 1) JSON yanıtını işle
                    let profiles = JSON.parse(response);
                    window.profileItems = profiles;

                    // 2) Eski profil öğelerini temizle
                    const profileListDiv = document.getElementById("profile-list");
                    profileListDiv.innerHTML = "";

                    // 3) Yeni profilleri ekle
                    profiles.forEach(function(p) {
                        let div = document.createElement("div");
                        div.className = "profile-item";
                        div.textContent = p.displayName;
                        div.setAttribute("data-profile-path", p.path);
                        div.addEventListener("click", function() {
                            profileBridge.openProfileEditor(p.path);
                        });
                        profileListDiv.appendChild(div);
                    });
                }


                window.updateProfileItemText = function(path, newName) {
                    // 1) İç veriyi güncelle
                    window.profileItems.forEach(item => {
                        if (item.path === path) {
                            item.displayName = newName;
                        }
                    });
                    // 2) DOM'daki öğeyi güncelle
                    document.querySelectorAll('.profile-item').forEach(el => {
                        if (el.getAttribute('data-profile-path') === path) {
                            el.textContent = newName;
                        }
                    });
                };


                platformSelect.addEventListener("change", function() {
                    let selected = platformSelect.value;
                    profileBridge.getProfiles(selected, function(response) {
                        updateProfileList(response);
                    });
                });

                // Sayfa yüklenince varsayılan "instagram" profilleri göster
                platformSelect.value = "instagram";
                profileBridge.getProfiles("instagram", function(response) {
                    updateProfileList(response);
                });

                // --------------------------------------------------
                // DESTEK BUTONU TIKLANDIĞINDA PYTHONDAKİ FONKSİYON ÇAĞRILACAK
                // --------------------------------------------------
                document.getElementById("errorBtn").addEventListener("click", function() {
                    pyBridge.openDestekWindow();
                });
            });
        });
    </script>
</body>
</html>
"""


class DestekDialog(QDialog):
    """
    Kullanıcının e-posta adresini ve mesajını alıp Telegram'a ileten destek penceresi.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setWindowModality(Qt.ApplicationModal)
        self.setModal(True)
        self.resize(400, 300)

        shadow = QGraphicsDropShadowEffect(self)
        shadow.setOffset(0, 0)
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 180))
        self.setGraphicsEffect(shadow)

        self.mainLayout = QVBoxLayout(self)
        self.mainLayout.setContentsMargins(20, 20, 20, 20)
        self.mainLayout.setSpacing(15)

        self.titleLabel = QLabel("DESTEK")
        self.titleLabel.setStyleSheet("font-size: 18px; font-weight: bold; color: #2196F3; margin-bottom: 12px;")
        self.titleLabel.setAlignment(Qt.AlignCenter)
        self.mainLayout.addWidget(self.titleLabel)

        self.infoLabel = QLabel("")
        self.infoLabel.setStyleSheet("font-size: 13px; color: #39FF14; font-weight: bold;")
        self.infoLabel.setAlignment(Qt.AlignCenter)
        self.mainLayout.addWidget(self.infoLabel)

        # Email input
        self.emailLabel = QLabel("E-Posta Adresiniz:")
        self.emailLabel.setStyleSheet("font-size: 13px; font-weight: bold; color: #FFFFFF;")
        self.emailEdit = QLineEdit()
        self.emailEdit.setFixedHeight(40)
        self.emailEdit.setStyleSheet(
            "background-color: #1E1E1E; border: 1px solid #373737; border-radius: 6px;"
            "padding: 8px; color: #FFFFFF; font-size: 13px; qproperty-placeholderTextColor: #AAAAAA;"
        )
        self.emailEdit.setPlaceholderText("<EMAIL>")

        # Mesaj input
        self.messageLabel = QLabel("Mesajınız:")
        self.messageLabel.setStyleSheet("font-size: 13px; font-weight: bold; color: #FFFFFF;")
        self.messageEdit = QPlainTextEdit()
        self.messageEdit.setFixedHeight(100)
        self.messageEdit.setStyleSheet(
            "background-color: #1E1E1E; border: 1px solid #373737; border-radius: 6px;"
            "padding: 8px; color: #FFFFFF; font-size: 13px; qproperty-placeholderTextColor: #AAAAAA;"
        )
        self.messageEdit.setPlaceholderText("Lütfen mesajınızı buraya yazın...")

        formLayout = QFormLayout()
        formLayout.setSpacing(8)
        formLayout.addRow(self.emailLabel, self.emailEdit)
        formLayout.addRow(self.messageLabel, self.messageEdit)
        self.mainLayout.addLayout(formLayout)

        # Buttons
        self.buttonLayout = QHBoxLayout()
        self.sendBtn = QPushButton("Gönder")
        self.closeBtn = QPushButton("Kapat")

        btnStyle = """
            QPushButton {
                background-color: #2B2B2B;
                border: 1px solid #373737;
                border-radius: 6px;
                padding: 8px 14px;
                color: #FFFFFF;
                font-size: 13px;
                font-weight: bold;
                min-width: 90px;
                transition: background 0.3s, border-color 0.3s;
            }
            QPushButton:hover {
                background-color: #2196F3;
                border-color: #2196F3;
                color: #FFFFFF;
            }
        """
        self.sendBtn.setStyleSheet(btnStyle)
        self.closeBtn.setStyleSheet(btnStyle)

        self.buttonLayout.addStretch()
        self.buttonLayout.addWidget(self.sendBtn)
        self.buttonLayout.addWidget(self.closeBtn)
        self.buttonLayout.addStretch()

        self.mainLayout.addLayout(self.buttonLayout)

        self.sendBtn.clicked.connect(self.sendMessage)
        self.closeBtn.clicked.connect(self.close)

        self.setStyleSheet(
            """
            QDialog {
                background-color: #2A2A2A;
                color: #FFFFFF;
                border: 2px solid #2196F3;
                border-radius: 12px;
            }
            """
        )

        self.dragPos = None

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.dragPos = event.globalPos()
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        self.dragPos = None
        super().mouseReleaseEvent(event)

    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton and self.dragPos is not None:
            delta = event.globalPos() - self.dragPos
            self.move(self.pos() + delta)
            self.dragPos = event.globalPos()
        super().mouseMoveEvent(event)

    def sendMessage(self):
        email = self.emailEdit.text().strip()
        message_text = self.messageEdit.toPlainText().strip()
        if not message_text:
            self.infoLabel.setText("Mesaj boş olamaz.")
            QTimer.singleShot(2000, lambda: self.infoLabel.setText(""))
            return

        full_message = f"E-Posta: {email}\nMesaj: {message_text}"
        send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, full_message)

        self.infoLabel.setText("Gönderildi!")
        QTimer.singleShot(2000, lambda: self.infoLabel.setText(""))
        # İsterseniz formu temizleyebilirsiniz:
        self.emailEdit.clear()
        self.messageEdit.clear()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.FramelessWindowHint)

        # Uygulamanın temel ve minimum boyutları
        BASE_APP_WIDTH = 900.0
        BASE_APP_HEIGHT = 600.0
        ASPECT_RATIO = BASE_APP_HEIGHT / BASE_APP_WIDTH

        MIN_PREFERRED_WIDTH = 800.0
        MIN_PREFERRED_HEIGHT = MIN_PREFERRED_WIDTH * ASPECT_RATIO

        current_screen_geometry = None
        primary_screen = QApplication.primaryScreen()
        if primary_screen:
            current_screen_geometry = primary_screen.availableGeometry()

        if current_screen_geometry:
            available_width = float(current_screen_geometry.width())
            available_height = float(current_screen_geometry.height())

            target_width = BASE_APP_WIDTH
            target_height = BASE_APP_HEIGHT

            # Eğer temel boyutlar ekrana sığmıyorsa, sığacak şekilde ölçekle
            if target_width > available_width or target_height > available_height:
                scale_factor_to_fit = min(available_width / target_width, available_height / target_height)
                target_width = target_width * scale_factor_to_fit
                target_height = target_height * scale_factor_to_fit

            # Nihai boyutları belirle (target_width/height zaten ekrana sığıyor)
            final_width = target_width
            final_height = target_height

            # Eğer ölçeklenmiş boyutlar tercih edilen minimumdan küçükse
            # ve tercih edilen minimum ekrana sığıyorsa, minimumu kullan.
            if (final_width < MIN_PREFERRED_WIDTH or final_height < MIN_PREFERRED_HEIGHT):
                if MIN_PREFERRED_WIDTH <= available_width and MIN_PREFERRED_HEIGHT <= available_height:
                    final_width = MIN_PREFERRED_WIDTH
                    final_height = MIN_PREFERRED_HEIGHT
                # Else: Tercih edilen minimum ekrana sığmıyor, bu yüzden zaten ekrana sığan
                # `target_width` ve `target_height` değerleri kullanılacak (yukarıda final_width/height olarak atandı).

            self.resize(int(final_width), int(final_height))

        else:
            # Ekran bilgisi alınamazsa orijinal sabit boyuta geri dön
            self.resize(int(BASE_APP_WIDTH), int(BASE_APP_HEIGHT))

        self.setWindowTitle("3 Deck UI - Dark Theme (Modernized)")
        self.view = QWebEngineView()
        self.setCentralWidget(self.view)
        self.channel = QWebChannel(self.view.page())
        self.bridge = JsBridge(self)
        self.profileBridge = ProfileBridge(self)
        self.channel.registerObject("pyBridge", self.bridge)
        self.channel.registerObject("profileBridge", self.profileBridge)
        self.view.page().setWebChannel(self.channel)
        self.view.setHtml(html_content)
        self.view.loadFinished.connect(self.on_load_finished)
        self.centerWindow()

        # İstatistik sistemi için timer başlat
        self.stats_timer = QTimer(self)
        self.stats_timer.timeout.connect(self.update_stats)
        self.stats_timer.start(20000)  # 10 saniyede bir kontrol et

        # İstatistik sistemini başlat
        initialize_stats_system()
        # İstatistikler yüklenir yüklenmez anında göster
        self.update_stats()

        # 0.5 saniye sonra durumu 'stopped' olarak ayarla (başlangıç animasyonunu bastırmak için)
        QTimer.singleShot(500, lambda: self.set_status_bar_color(False))
        self.set_status_bar_color(False)  # Garantili kırmızı başlat

    def on_load_finished(self):
        self.set_status_bar_color(False)

    def set_status_bar_color(self, is_working):
        if is_working is None:
            state = "stopped"  # Başlangıçta da kırmızı yap
        else:
            state = "working" if is_working else "stopped"
        js = f'setStatusBarColor("{state}");'
        self.view.page().runJavaScript(js)

    def centerWindow(self):
        frame_geom = self.frameGeometry()
        screen_center = QDesktopWidget().availableGeometry().center()
        frame_geom.moveCenter(screen_center)
        self.move(frame_geom.topLeft())

    def update_stats(self):
        try:
            # Profilleri kontrol et ve yeni HTML oluştur (Can Barları için - extra-deck)
            check_profiles(force=True)
            html_can_barlari = update_stats_display_html()
            # Skeleton animasyonunu kaldırıp can barlarını güncelle (extra-deck)
            self.view.page().runJavaScript(f'''
                (function() {{
                    const extraDeck = document.querySelector(".extra-deck");
                    if (!extraDeck) return;
                    const newHtml = {json.dumps(html_can_barlari)};
                    // extraDeck'in içeriğini tamamen yeni HTML ile değiştir
                    extraDeck.innerHTML = newHtml;
                }})();
            ''')

            # Var olan profil anahtarlarını bulmak için mevcut JSON dosyalarını kontrol et
            base_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio\\configuration"
            mevcut_profiller = set()
            for platform in ["instagram", "twitter"]:
                platform_dir = os.path.join(base_dir, platform)
                if not os.path.exists(platform_dir):
                    continue
                for file in os.listdir(platform_dir):
                    if file.endswith(".json"):
                        try:
                            with open(os.path.join(platform_dir, file), "r", encoding="utf-8") as f:
                                data = json.load(f)
                                username = data.get("username", "").strip()
                                if username:
                                    profile_key = f"{platform}_{username}"
                                    mevcut_profiller.add(profile_key)
                        except Exception as e:
                            logging.error(f"Profil dosyası okuma hatası {file}: {str(e)}")
                            pass

            # stats_system verilerini ve stats_display_queue'yu temizle
            # Sadece mevcut_profiller'de olan VE stats_data'da verisi olanları tut

            # 1. stats_data'yı temizle
            silinecek_keys_stats_data = [
                key for key in list(stats_system.stats_data.keys()) if key not in mevcut_profiller
            ]
            updated_during_cleanup = False
            for key in silinecek_keys_stats_data:
                del stats_system.stats_data[key]
                if key in stats_system.last_stats_update:
                    del stats_system.last_stats_update[key]
                if key in stats_system.profiles_to_process:
                    stats_system.profiles_to_process.discard(key)
                updated_during_cleanup = True
                logging.info(f"UI güncellemesi sırasında var olmayan profile ait istatistik (data) temizlendi: {key}")

            # 2. stats_display_queue'yu yeniden oluştur
            old_queue_len = len(stats_system.stats_display_queue)
            new_display_queue = deque()
            processed_in_queue = set()  # Kuyrukta mükerrerliği önlemek için
            for key_in_q in list(stats_system.stats_display_queue):  # Orijinal kuyruğun kopyası üzerinde iterasyon
                if key_in_q in mevcut_profiller and key_in_q in stats_system.stats_data and key_in_q not in processed_in_queue:
                    new_display_queue.append(key_in_q)
                    processed_in_queue.add(key_in_q)
            stats_system.stats_display_queue = new_display_queue
            if len(stats_system.stats_display_queue) != old_queue_len:
                updated_during_cleanup = True

            if updated_during_cleanup:
                stats_system.istatistikleri_kaydet()  # Değişiklik olduysa dosyayı kaydet

            # current_displayed_profile'ın hala geçerli olup olmadığını kontrol et
            if stats_system.current_displayed_profile and \
                    (stats_system.current_displayed_profile not in mevcut_profiller or \
                     stats_system.current_displayed_profile not in stats_system.stats_data):
                stats_system.current_displayed_profile = next(iter(stats_system.stats_display_queue), None)

            # --- Sağ deck'teki (istatistikler) detaylı gösterim mantığı ---
            if not stats_system.stats_display_queue:
                # Gösterilecek geçerli ve verisi olan profil yoksa, iskelet HTML'ini sağ deck'e bas
                self.set_right_deck_to_skeleton_html()
            else:
                # Gösterilecek profil(ler) var:
                profile_key_to_display = stats_system.stats_display_queue.popleft()
                stats_system.stats_display_queue.append(profile_key_to_display)  # Rotasyon için sona ekle
                stats_system.current_displayed_profile = profile_key_to_display  # Takip için güncelle

                stats_data = stats_system.stats_data.get(profile_key_to_display)

                right_deck_content_html = ""  # .right-deck'in tamamı için HTML

                if stats_data:
                    platform = stats_data.get("platform")
                    stats_specific_html = ""  # Sadece ilgili platformun istatistik HTML'i

                    if platform == "twitter":
                        stats_specific_html = format_twitter_stats_as_boxes(stats_data)
                        # .right-deck için tam HTML: Twitter stats + boş Instagram stats div
                        right_deck_content_html = f'''
                            <div class="stats-grid twitter-stats">{stats_specific_html}</div>
                            <div class="stats-grid instagram-stats"></div>
                        '''
                    elif platform == "instagram":
                        stats_specific_html = format_instagram_stats_as_two_columns(stats_data)
                        # .right-deck için tam HTML: Boş Twitter stats div + Instagram stats
                        right_deck_content_html = f'''
                            <div class="stats-grid twitter-stats"></div>
                            <div class="stats-grid instagram-stats">{stats_specific_html}</div>
                        '''

                    if right_deck_content_html:
                        self.view.page().runJavaScript(f'''
                            var rightDeck = document.querySelector(".right-deck");
                            if (rightDeck) {{
                                rightDeck.innerHTML = {json.dumps(right_deck_content_html)};
                            }}
                        ''')
                    else:
                        # Bu durum stats_data geçerli ama platform bilinmiyorsa olabilir (normalde olmamalı)
                        logging.warning(
                            f"Geçerli platform için HTML oluşturulamadı: {platform}. İskelet gösteriliyor.");
                        self.set_right_deck_to_skeleton_html()
                else:
                    # stats_data beklenmedik şekilde null ise (kuyruk temizliğine rağmen).
                    logging.warning(
                        f"İstatistik verisi {profile_key_to_display} için bulunamadı (beklenmedik). İskelet gösteriliyor.");
                    self.set_right_deck_to_skeleton_html()
        except Exception as e:
            logging.error(f"İstatistik güncellenirken genel hata oluştu: {str(e)}")
            # Genel bir hata durumunda da sağ deck'i iskelet moduna almak güvenlidir.
            self.set_right_deck_to_skeleton_html()

    def set_right_deck_to_skeleton_html(self):
        """
        Sağ deck'in (istatistikler) içeriğini sadece iskelet animasyonu ile doldurur.
        """
        skeleton_html_for_right_deck = '''
        <div class="skeleton-container" id="right-skeleton">
            <div class="skeleton" style="width: 85%; height: 45px;"></div>
            <div class="skeleton" style="width: 80%; height: 45px;"></div>
            <div class="skeleton" style="width: 75%; height: 45px;"></div>
        </div>
        '''
        try:
            self.view.page().runJavaScript(f'''
                var rightDeck = document.querySelector(".right-deck");
                if (rightDeck) {{
                    rightDeck.innerHTML = {json.dumps(skeleton_html_for_right_deck)};
                }}
            ''')
        except Exception as e:
            logging.error(f"Sağ deck iskelet HTML'i ayarlanırken hata: {e}")

    @pyqtSlot()
    def startProcessing(self):
        """
        'Başla' butonuna basıldığında:
          1) Tüm profillerdeki linkleri indirir
          2) Her profildeki schedule saatine göre paylaşım görevlerini planlar
          3) Scheduler başlatılır
        """
        # 1) Tüm profillerdeki linkleri hemen indir
        threading.Thread(target=download_all_profiles_links, daemon=True).start()

        # 2) Scheduler oluştur ve job'ları tanımla
        if not hasattr(self, 'scheduler'):
            self.scheduler = BackgroundScheduler()

        self.scheduler.remove_all_jobs()  # Eski job'ları temizle

        schedule_all_profiles_uploads(self.scheduler)  # Job'ları yükle

        try:
            self.scheduler.start()
            logging.info("Scheduler başlatıldı.")
        except Exception as e:
            logging.error(f"Scheduler başlatılırken hata oluştu: {e}")

    @pyqtSlot()
    def stopProcessing(self):
        # Scheduler'ı durdur
        if hasattr(self, 'scheduler'):
            try:
                self.scheduler.shutdown(wait=False)
                logging.info("Scheduler durduruldu.")
            except Exception as e:
                logging.error(f"Scheduler durdurulurken hata: {e}")
        # UI durumunu 'stopped' olarak güncelle ve log mesajı yaz
        self.set_status_bar_color(False)
        logging.info("İşlem durduruldu.")


def reload_scheduler(self):
    if hasattr(self, "scheduler"):
        self.scheduler.remove_all_jobs()
    else:
        self.scheduler = BackgroundScheduler()
    schedule_all_profiles_uploads(self.scheduler)
    self.scheduler.start()


def threaded_download_all_profiles_links(self):
    thread = threading.Thread(target=download_all_profiles_links)
    thread.start()


def threaded_upload_all_profiles_links(self):
    thread = threading.Thread(target=upload_all_profiles_links)
    thread.start()


def threaded_process_profile_links(self, platform, output_dir, temp_dir, ffmpeg_dir):
    thread = threading.Thread(
        target=self.process_profile_links,
        args=(platform, output_dir, temp_dir, ffmpeg_dir),
    )
    thread.daemon = True
    thread.start()

    @pyqtSlot()
    def trigger_stats_update(self):
        logging.info("ProfileEditorDialog'dan tetiklenen anlık istatistik ve arayüz güncellemesi çağrıldı.")
        # update_stats zaten check_profiles'ı çağırıyor ve sonra HTML'i güncelliyor.
        # Bu, yeni eklenen/güncellenen profilin istatistiklerinin çekilmesini
        # ve arayüzde gösterilmesini sağlamalı.
        self.update_stats()

    def on_load_finished(self):
        self.set_status_bar_color(False)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    logger = setup_logging()
    setup_configurations()

    # Start Instagram session initialization in a background thread
    insta_session_thread = threading.Thread(target=initialize_sessions_on_startup, daemon=True)
    insta_session_thread.start()

    # Start Twitter session initialization in a background thread
    twitter_session_thread = threading.Thread(target=initialize_twitter_sessions_on_startup, daemon=True)
    twitter_session_thread.start()

    # Twitter oturum meta dosyalarını periyodik kontrol eden thread
    def twitter_session_refresh_daemon():
        import time, os, json, random, shutil
        from datetime import datetime, timedelta
        base_config_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio\\configuration\\twitter"
        session_dir = os.path.join(base_config_dir, "sessions")
        while True:
            try:
                if not os.path.exists(session_dir):
                    time.sleep(3600)
                    continue
                for meta_file in os.listdir(session_dir):
                    if not meta_file.endswith("_session.meta.json"): continue
                    meta_path = os.path.join(session_dir, meta_file)
                    try:
                        with open(meta_path, "r", encoding="utf-8") as mf:
                            meta = json.load(mf)
                        created = meta.get("created")
                        next_refresh = meta.get("next_refresh")
                        username = meta_file.replace("_session.meta.json", "")
                        if created:
                            created_dt = datetime.strptime(created, "%Y-%m-%d %H:%M:%S")
                            now = datetime.now()
                            # Yenileme zamanı gelmiş mi?
                            if next_refresh and now >= datetime.strptime(next_refresh, "%Y-%m-%d %H:%M:%S"):
                                # Oturum klasörünü sil
                                base_project_dir = "C:\\Users\\<USER>\\PycharmProjects\\Sorcerio"
                                user_profile_dir_path = os.path.join(base_project_dir, f"chrome_profile_{username}")
                                if os.path.exists(user_profile_dir_path):
                                    try:
                                        shutil.rmtree(user_profile_dir_path)
                                    except Exception:
                                        pass
                                # Meta güncelle
                                meta["created"] = now.strftime("%Y-%m-%d %H:%M:%S")
                                meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                                meta["next_refresh"] = ""
                                with open(meta_path, "w", encoding="utf-8") as mf:
                                    json.dump(meta, mf, indent=4, ensure_ascii=False)
                    except Exception:
                        continue
            except Exception:
                pass
            time.sleep(3600)  # Her saat başı kontrol et

    twitter_session_refresh_thread = threading.Thread(target=twitter_session_refresh_daemon, daemon=True)
    twitter_session_refresh_thread.start()

    window = MainWindow()
    window.show()
    sys.exit(app.exec_())